/// Data structure for level configuration
class LevelData {
  final int levelNumber;
  final String name;
  final String description;
  final List<BirdData> birds;
  final List<PigData> pigs;
  final List<BlockData> blocks;
  final int oneStarScore;
  final int twoStarScore;
  final int threeStarScore;
  final String? backgroundImage;
  final Map<String, dynamic>? metadata;
  
  LevelData({
    required this.levelNumber,
    required this.name,
    required this.description,
    required this.birds,
    required this.pigs,
    required this.blocks,
    required this.oneStarScore,
    required this.twoStarScore,
    required this.threeStarScore,
    this.backgroundImage,
    this.metadata,
  });
  
  /// Create LevelData from JSON
  factory LevelData.fromJson(Map<String, dynamic> json) {
    return LevelData(
      levelNumber: json['levelNumber'] ?? 1,
      name: json['name'] ?? 'Unnamed Level',
      description: json['description'] ?? '',
      birds: (json['birds'] as List<dynamic>?)
          ?.map((bird) => BirdData.fromJson(bird))
          .toList() ?? [],
      pigs: (json['pigs'] as List<dynamic>?)
          ?.map((pig) => PigData.fromJson(pig))
          .toList() ?? [],
      blocks: (json['blocks'] as List<dynamic>?)
          ?.map((block) => BlockData.fromJson(block))
          .toList() ?? [],
      oneStarScore: json['oneStarScore'] ?? 10000,
      twoStarScore: json['twoStarScore'] ?? 20000,
      threeStarScore: json['threeStarScore'] ?? 30000,
      backgroundImage: json['backgroundImage'],
      metadata: json['metadata'],
    );
  }
  
  /// Convert LevelData to JSON
  Map<String, dynamic> toJson() {
    return {
      'levelNumber': levelNumber,
      'name': name,
      'description': description,
      'birds': birds.map((bird) => bird.toJson()).toList(),
      'pigs': pigs.map((pig) => pig.toJson()).toList(),
      'blocks': blocks.map((block) => block.toJson()).toList(),
      'oneStarScore': oneStarScore,
      'twoStarScore': twoStarScore,
      'threeStarScore': threeStarScore,
      'backgroundImage': backgroundImage,
      'metadata': metadata,
    };
  }
}

/// Data structure for bird configuration
class BirdData {
  final String type; // 'red', 'blue', 'yellow', 'black', 'white'
  final Map<String, double> position; // x, y coordinates
  final Map<String, dynamic>? properties; // Special properties
  
  BirdData({
    required this.type,
    required this.position,
    this.properties,
  });
  
  factory BirdData.fromJson(Map<String, dynamic> json) {
    return BirdData(
      type: json['type'] ?? 'red',
      position: Map<String, double>.from(json['position'] ?? {'x': 0.0, 'y': 0.0}),
      properties: json['properties'],
    );
  }
  
  Map<String, dynamic> toJson() {
    return {
      'type': type,
      'position': position,
      'properties': properties,
    };
  }
  
  double get x => position['x'] ?? 0.0;
  double get y => position['y'] ?? 0.0;
}

/// Data structure for pig configuration
class PigData {
  final String type; // 'basic', 'helmet', 'king'
  final Map<String, double> position; // x, y coordinates
  final int health;
  final double rotation;
  final Map<String, dynamic>? properties;
  
  PigData({
    required this.type,
    required this.position,
    required this.health,
    this.rotation = 0.0,
    this.properties,
  });
  
  factory PigData.fromJson(Map<String, dynamic> json) {
    return PigData(
      type: json['type'] ?? 'basic',
      position: Map<String, double>.from(json['position'] ?? {'x': 0.0, 'y': 0.0}),
      health: json['health'] ?? 100,
      rotation: (json['rotation'] ?? 0.0).toDouble(),
      properties: json['properties'],
    );
  }
  
  Map<String, dynamic> toJson() {
    return {
      'type': type,
      'position': position,
      'health': health,
      'rotation': rotation,
      'properties': properties,
    };
  }
  
  double get x => position['x'] ?? 0.0;
  double get y => position['y'] ?? 0.0;
}

/// Data structure for block configuration
class BlockData {
  final String type; // 'wood', 'stone', 'ice', 'glass'
  final Map<String, double> position; // x, y coordinates
  final double rotation;
  final Map<String, double>? size; // width, height
  final Map<String, dynamic>? properties;
  
  BlockData({
    required this.type,
    required this.position,
    this.rotation = 0.0,
    this.size,
    this.properties,
  });
  
  factory BlockData.fromJson(Map<String, dynamic> json) {
    return BlockData(
      type: json['type'] ?? 'wood',
      position: Map<String, double>.from(json['position'] ?? {'x': 0.0, 'y': 0.0}),
      rotation: (json['rotation'] ?? 0.0).toDouble(),
      size: json['size'] != null ? Map<String, double>.from(json['size']) : null,
      properties: json['properties'],
    );
  }
  
  Map<String, dynamic> toJson() {
    return {
      'type': type,
      'position': position,
      'rotation': rotation,
      'size': size,
      'properties': properties,
    };
  }
  
  double get x => position['x'] ?? 0.0;
  double get y => position['y'] ?? 0.0;
  double get width => size?['width'] ?? 1.0;
  double get height => size?['height'] ?? 1.0;
}

/// Helper class for level validation
class LevelValidator {
  /// Validate level data
  static List<String> validateLevel(LevelData level) {
    List<String> errors = [];
    
    // Check basic requirements
    if (level.birds.isEmpty) {
      errors.add('Level must have at least one bird');
    }
    
    if (level.pigs.isEmpty) {
      errors.add('Level must have at least one pig');
    }
    
    // Check score thresholds
    if (level.oneStarScore >= level.twoStarScore) {
      errors.add('Two star score must be higher than one star score');
    }
    
    if (level.twoStarScore >= level.threeStarScore) {
      errors.add('Three star score must be higher than two star score');
    }
    
    // Check bird types
    for (BirdData bird in level.birds) {
      if (!_isValidBirdType(bird.type)) {
        errors.add('Invalid bird type: ${bird.type}');
      }
    }
    
    // Check pig types
    for (PigData pig in level.pigs) {
      if (!_isValidPigType(pig.type)) {
        errors.add('Invalid pig type: ${pig.type}');
      }
    }
    
    // Check block types
    for (BlockData block in level.blocks) {
      if (!_isValidBlockType(block.type)) {
        errors.add('Invalid block type: ${block.type}');
      }
    }
    
    return errors;
  }
  
  static bool _isValidBirdType(String type) {
    return ['red', 'blue', 'yellow', 'black', 'white'].contains(type);
  }
  
  static bool _isValidPigType(String type) {
    return ['basic', 'helmet', 'king'].contains(type);
  }
  
  static bool _isValidBlockType(String type) {
    return ['wood', 'stone', 'ice', 'glass'].contains(type);
  }
}
