import 'package:flutter_test/flutter_test.dart';
import 'package:angry_birds_game/utils/scoring_system.dart';

void main() {
  group('ScoringSystem Tests', () {
    test('calculatePigScore returns correct base score', () {
      expect(ScoringSystem.calculatePigScore('basic'), equals(5000));
      expect(ScoringSystem.calculatePigScore('helmet'), equals(10000));
      expect(ScoringSystem.calculatePigScore('king'), equals(15000));
    });

    test('calculatePigScore applies combo bonus correctly', () {
      int baseScore = ScoringSystem.calculatePigScore('basic');
      int comboScore = ScoringSystem.calculatePigScore('basic', comboCount: 3);
      
      expect(comboScore, greaterThan(baseScore));
    });

    test('calculateBlockScore returns correct base score', () {
      expect(ScoringSystem.calculateBlockScore('wood'), equals(500));
      expect(ScoringSystem.calculateBlockScore('stone'), equals(1000));
      expect(ScoringSystem.calculateBlockScore('ice'), equals(400));
      expect(ScoringSystem.calculateBlockScore('glass'), equals(250));
    });

    test('calculateBirdBonus returns correct bonus', () {
      expect(ScoringSystem.calculateBirdBonus(0), equals(0));
      expect(ScoringSystem.calculateBirdBonus(1), equals(10000));
      expect(ScoringSystem.calculateBirdBonus(3), equals(30000));
    });

    test('calculateStars returns correct star count', () {
      expect(ScoringSystem.calculateStars(5000, 10000, 20000, 30000), equals(0));
      expect(ScoringSystem.calculateStars(15000, 10000, 20000, 30000), equals(1));
      expect(ScoringSystem.calculateStars(25000, 10000, 20000, 30000), equals(2));
      expect(ScoringSystem.calculateStars(35000, 10000, 20000, 30000), equals(3));
    });

    test('calculateDestructionBonus returns correct bonus', () {
      expect(ScoringSystem.calculateDestructionBonus(1.0), equals(5000));
      expect(ScoringSystem.calculateDestructionBonus(0.9), equals(2000));
      expect(ScoringSystem.calculateDestructionBonus(0.7), equals(1000));
      expect(ScoringSystem.calculateDestructionBonus(0.5), equals(0));
    });

    test('calculateTimeBonus returns correct bonus', () {
      expect(ScoringSystem.calculateTimeBonus(30.0, 60.0), equals(1500));
      expect(ScoringSystem.calculateTimeBonus(60.0, 60.0), equals(0));
      expect(ScoringSystem.calculateTimeBonus(90.0, 60.0), equals(0));
    });

    test('calculateLevelScore returns complete score breakdown', () {
      LevelScore score = ScoringSystem.calculateLevelScore(
        pigScore: 10000,
        blockScore: 2000,
        unusedBirds: 2,
        destructionPercentage: 1.0,
        timeElapsed: 30.0,
        targetTime: 60.0,
        oneStarThreshold: 10000,
        twoStarThreshold: 20000,
        threeStarThreshold: 30000,
      );

      expect(score.pigScore, equals(10000));
      expect(score.blockScore, equals(2000));
      expect(score.birdBonus, equals(20000));
      expect(score.destructionBonus, equals(5000));
      expect(score.timeBonus, equals(1500));
      expect(score.totalScore, greaterThan(30000));
      expect(score.stars, equals(3));
    });
  });

  group('ComboTracker Tests', () {
    test('tracks combo correctly', () {
      ComboTracker tracker = ComboTracker();
      
      tracker.addDestruction(0.0);
      expect(tracker.getCurrentCombo(), equals(1));
      
      tracker.addDestruction(1.0);
      expect(tracker.getCurrentCombo(), equals(2));
      
      tracker.addDestruction(2.0);
      expect(tracker.getCurrentCombo(), equals(3));
    });

    test('resets combo after time window', () {
      ComboTracker tracker = ComboTracker();
      
      tracker.addDestruction(0.0);
      expect(tracker.getCurrentCombo(), equals(1));
      
      // Add destruction after time window
      tracker.addDestruction(5.0);
      expect(tracker.getCurrentCombo(), equals(1)); // Should reset
    });

    test('isComboActive works correctly', () {
      ComboTracker tracker = ComboTracker();
      
      tracker.addDestruction(0.0);
      expect(tracker.isComboActive(1.0), isTrue);
      expect(tracker.isComboActive(3.0), isFalse);
    });
  });

  group('LevelScore Tests', () {
    test('toMap returns correct data', () {
      LevelScore score = LevelScore(
        pigScore: 1000,
        blockScore: 500,
        birdBonus: 2000,
        destructionBonus: 1000,
        timeBonus: 500,
        totalScore: 5000,
        stars: 2,
        destructionPercentage: 0.8,
      );

      Map<String, dynamic> map = score.toMap();
      
      expect(map['pigScore'], equals(1000));
      expect(map['blockScore'], equals(500));
      expect(map['birdBonus'], equals(2000));
      expect(map['destructionBonus'], equals(1000));
      expect(map['timeBonus'], equals(500));
      expect(map['totalScore'], equals(5000));
      expect(map['stars'], equals(2));
      expect(map['destructionPercentage'], equals(0.8));
    });

    test('toString returns formatted string', () {
      LevelScore score = LevelScore(
        pigScore: 1000,
        blockScore: 500,
        birdBonus: 2000,
        destructionBonus: 1000,
        timeBonus: 500,
        totalScore: 5000,
        stars: 2,
        destructionPercentage: 0.8,
      );

      String str = score.toString();
      expect(str, contains('5000'));
      expect(str, contains('2'));
    });
  });

  group('AchievementSystem Tests', () {
    test('checkAchievements returns correct achievements', () {
      LevelScore perfectScore = LevelScore(
        pigScore: 1000,
        blockScore: 500,
        birdBonus: 2000,
        destructionBonus: 1000,
        timeBonus: 500,
        totalScore: 5000,
        stars: 3,
        destructionPercentage: 1.0,
      );

      List<Achievement> achievements = AchievementSystem.checkAchievements(
        perfectScore,
        25.0, // Fast time
        1,    // Only one bird used
      );

      expect(achievements.length, greaterThan(0));
      
      // Check for specific achievements
      bool hasPerfectLevel = achievements.any((a) => a.id == 'perfect_level');
      bool hasSpeedDemon = achievements.any((a) => a.id == 'speed_demon');
      bool hasBirdSaver = achievements.any((a) => a.id == 'bird_saver');
      
      expect(hasPerfectLevel, isTrue);
      expect(hasSpeedDemon, isTrue);
      expect(hasBirdSaver, isTrue);
    });
  });
}
