import 'package:flame/components.dart';
import 'package:flutter/material.dart';

/// Level complete overlay
class LevelCompleteOverlay extends Component {
  final int score;
  final int stars;
  final VoidCallback onNextLevel;
  final VoidCallback onRestart;
  final VoidCallback onMenu;

  LevelCompleteOverlay({
    required this.score,
    required this.stars,
    required this.onNextLevel,
    required this.onRestart,
    required this.onMenu,
  });

  @override
  void render(Canvas canvas) {
    super.render(canvas);
    
    // Draw semi-transparent background
    final backgroundPaint = Paint()
      ..color = Colors.black.withOpacity(0.7);
    
    canvas.drawRect(
      Rect.fromLTWH(0, 0, 1000, 1000), // Large enough to cover screen
      backgroundPaint,
    );
    
    // Draw level complete panel
    final panelPaint = Paint()
      ..color = Colors.green[400]!;
    
    final panelRect = Rect.fromCenter(
      center: const Offset(400, 300),
      width: 300,
      height: 200,
    );
    
    canvas.drawRRect(
      RRect.fromRectAndRadius(panelRect, const Radius.circular(20)),
      panelPaint,
    );
    
    // Draw text (simplified - in a real game you'd use proper text rendering)
    final textPainter = TextPainter(
      text: TextSpan(
        text: 'Level Complete!\nScore: $score',
        style: const TextStyle(
          color: Colors.white,
          fontSize: 24,
          fontWeight: FontWeight.bold,
        ),
      ),
      textDirection: TextDirection.ltr,
    );
    
    textPainter.layout();
    textPainter.paint(canvas, const Offset(300, 250));
    
    // Draw stars
    _drawStars(canvas, stars);
  }
  
  void _drawStars(Canvas canvas, int starCount) {
    final starPaint = Paint()..color = Colors.yellow;
    
    for (int i = 0; i < 3; i++) {
      final starColor = i < starCount ? Colors.yellow : Colors.grey;
      final paint = Paint()..color = starColor;
      
      // Simple star representation (circle for now)
      canvas.drawCircle(
        Offset(350 + i * 40, 320),
        15,
        paint,
      );
    }
  }
}

/// Level failed overlay
class LevelFailedOverlay extends Component {
  final VoidCallback onRestart;
  final VoidCallback onMenu;

  LevelFailedOverlay({
    required this.onRestart,
    required this.onMenu,
  });

  @override
  void render(Canvas canvas) {
    super.render(canvas);
    
    // Draw semi-transparent background
    final backgroundPaint = Paint()
      ..color = Colors.black.withOpacity(0.7);
    
    canvas.drawRect(
      Rect.fromLTWH(0, 0, 1000, 1000), // Large enough to cover screen
      backgroundPaint,
    );
    
    // Draw level failed panel
    final panelPaint = Paint()
      ..color = Colors.red[400]!;
    
    final panelRect = Rect.fromCenter(
      center: const Offset(400, 300),
      width: 300,
      height: 150,
    );
    
    canvas.drawRRect(
      RRect.fromRectAndRadius(panelRect, const Radius.circular(20)),
      panelPaint,
    );
    
    // Draw text
    final textPainter = TextPainter(
      text: const TextSpan(
        text: 'Level Failed!\nTry Again?',
        style: TextStyle(
          color: Colors.white,
          fontSize: 24,
          fontWeight: FontWeight.bold,
        ),
      ),
      textDirection: TextDirection.ltr,
    );
    
    textPainter.layout();
    textPainter.paint(canvas, const Offset(320, 270));
  }
}

/// Pause overlay
class PauseOverlay extends Component {
  final VoidCallback onResume;
  final VoidCallback onRestart;
  final VoidCallback onMenu;

  PauseOverlay({
    required this.onResume,
    required this.onRestart,
    required this.onMenu,
  });

  @override
  void render(Canvas canvas) {
    super.render(canvas);
    
    // Draw semi-transparent background
    final backgroundPaint = Paint()
      ..color = Colors.black.withOpacity(0.5);
    
    canvas.drawRect(
      Rect.fromLTWH(0, 0, 1000, 1000),
      backgroundPaint,
    );
    
    // Draw pause panel
    final panelPaint = Paint()
      ..color = Colors.blue[400]!;
    
    final panelRect = Rect.fromCenter(
      center: const Offset(400, 300),
      width: 250,
      height: 200,
    );
    
    canvas.drawRRect(
      RRect.fromRectAndRadius(panelRect, const Radius.circular(20)),
      panelPaint,
    );
    
    // Draw text
    final textPainter = TextPainter(
      text: const TextSpan(
        text: 'PAUSED',
        style: TextStyle(
          color: Colors.white,
          fontSize: 32,
          fontWeight: FontWeight.bold,
        ),
      ),
      textDirection: TextDirection.ltr,
    );
    
    textPainter.layout();
    textPainter.paint(canvas, const Offset(350, 280));
  }
}

/// Score display component
class ScoreDisplay extends Component {
  int currentScore = 0;
  int targetScore = 0;
  
  void updateScore(int newScore) {
    targetScore = newScore;
  }
  
  @override
  void update(double dt) {
    super.update(dt);
    
    // Animate score counting up
    if (currentScore < targetScore) {
      int diff = targetScore - currentScore;
      int increment = (diff * dt * 5).ceil().clamp(1, 1000);
      currentScore = (currentScore + increment).clamp(0, targetScore);
    }
  }
  
  @override
  void render(Canvas canvas) {
    super.render(canvas);
    
    // Draw score background
    final backgroundPaint = Paint()
      ..color = Colors.black.withOpacity(0.6);
    
    canvas.drawRRect(
      RRect.fromRectAndRadius(
        const Rect.fromLTWH(10, 10, 150, 40),
        const Radius.circular(20),
      ),
      backgroundPaint,
    );
    
    // Draw score text
    final textPainter = TextPainter(
      text: TextSpan(
        text: 'Score: $currentScore',
        style: const TextStyle(
          color: Colors.white,
          fontSize: 16,
          fontWeight: FontWeight.bold,
        ),
      ),
      textDirection: TextDirection.ltr,
    );
    
    textPainter.layout();
    textPainter.paint(canvas, const Offset(20, 25));
  }
}
