import 'package:flame/components.dart';
import 'package:flame_forge2d/flame_forge2d.dart';
import 'package:flutter/material.dart';

import 'pig_component.dart';
import 'block_component.dart';

/// Base class for all bird types
abstract class BirdComponent extends BodyComponent with ContactCallbacks {
  final String birdType;
  final double radius;
  final double density;
  final Color color;
  final Vector2 initialPosition;

  // Bird state
  bool isLaunched = false;
  bool hasUsedSpecialAbility = false;
  double health = 100.0;

  BirdComponent({
    required this.birdType,
    required this.radius,
    required this.density,
    required this.color,
    Vector2? position,
  }) : initialPosition = position ?? Vector2.zero();
  
  @override
  Body createBody() {
    final bodyDef = BodyDef(
      type: BodyType.dynamic,
      position: initialPosition,
    );
    
    final body = world.createBody(bodyDef);
    
    // Create bird shape
    final shape = CircleShape()..radius = radius;
    
    final fixtureDef = FixtureDef(shape)
      ..density = density
      ..friction = 0.3
      ..restitution = 0.4;
    
    body.createFixture(fixtureDef);
    
    return body;
  }
  
  /// Launch the bird with given force
  void launch(Vector2 force) {
    if (!isLaunched) {
      body.applyLinearImpulse(force);
      isLaunched = true;
    }
  }
  
  /// Use special ability (override in subclasses)
  void useSpecialAbility() {
    if (!hasUsedSpecialAbility && isLaunched) {
      hasUsedSpecialAbility = true;
      performSpecialAbility();
    }
  }
  
  /// Perform the bird's special ability (override in subclasses)
  void performSpecialAbility() {
    // Default: no special ability
  }
  
  /// Take damage
  void takeDamage(double damage) {
    health -= damage;
    if (health <= 0) {
      destroy();
    }
  }
  
  /// Destroy the bird
  void destroy() {
    removeFromParent();
  }
  
  @override
  void beginContact(Object other, Contact contact) {
    super.beginContact(other, contact);
    
    // Handle collision with other objects
    if (other is PigComponent) {
      // Bird hit pig
      double impactForce = body.linearVelocity.length;
      other.takeDamage(impactForce * 10);
      takeDamage(impactForce * 5);
    } else if (other is BlockComponent) {
      // Bird hit block
      double impactForce = body.linearVelocity.length;
      other.takeDamage(impactForce * 8);
      takeDamage(impactForce * 3);
    }
  }
  
  @override
  void render(Canvas canvas) {
    super.render(canvas);
    
    // Draw bird
    final paint = Paint()
      ..color = color
      ..style = PaintingStyle.fill;
    
    canvas.drawCircle(Offset.zero, radius, paint);
    
    // Draw eyes
    final eyePaint = Paint()
      ..color = Colors.white
      ..style = PaintingStyle.fill;
    
    canvas.drawCircle(Offset(-radius * 0.3, -radius * 0.2), radius * 0.2, eyePaint);
    canvas.drawCircle(Offset(radius * 0.3, -radius * 0.2), radius * 0.2, eyePaint);
    
    // Draw pupils
    final pupilPaint = Paint()
      ..color = Colors.black
      ..style = PaintingStyle.fill;
    
    canvas.drawCircle(Offset(-radius * 0.3, -radius * 0.2), radius * 0.1, pupilPaint);
    canvas.drawCircle(Offset(radius * 0.3, -radius * 0.2), radius * 0.1, pupilPaint);
    
    // Draw beak
    final beakPaint = Paint()
      ..color = Colors.orange
      ..style = PaintingStyle.fill;
    
    final beakPath = Path()
      ..moveTo(radius * 0.8, 0)
      ..lineTo(radius * 1.2, -radius * 0.1)
      ..lineTo(radius * 1.2, radius * 0.1)
      ..close();
    
    canvas.drawPath(beakPath, beakPaint);
  }
}

/// Red bird - basic bird with no special ability
class RedBird extends BirdComponent {
  RedBird({Vector2? position})
      : super(
          birdType: 'red',
          radius: 0.3,
          density: 1.0,
          color: Colors.red,
          position: position,
        );
}

/// Blue bird - splits into three smaller birds
class BlueBird extends BirdComponent {
  BlueBird({Vector2? position})
      : super(
          birdType: 'blue',
          radius: 0.25,
          density: 0.8,
          color: Colors.blue,
          position: position,
        );
  
  @override
  void performSpecialAbility() {
    // Split into three birds
    Vector2 currentVelocity = body.linearVelocity;
    Vector2 currentPosition = body.position;
    
    // Create two additional birds
    for (int i = 0; i < 2; i++) {
      BlueBird newBird = BlueBird(position: currentPosition.clone());
      newBird.isLaunched = true;
      newBird.hasUsedSpecialAbility = true;
      
      // Apply slightly different velocities
      Vector2 newVelocity = currentVelocity.clone();
      newVelocity.rotate((i - 0.5) * 0.5); // Spread birds
      newBird.body.linearVelocity = newVelocity;
      
      parent?.add(newBird);
    }
  }
}

/// Yellow bird - speeds up when ability is used
class YellowBird extends BirdComponent {
  YellowBird({Vector2? position})
      : super(
          birdType: 'yellow',
          radius: 0.28,
          density: 0.9,
          color: Colors.yellow,
          position: position,
        );
  
  @override
  void performSpecialAbility() {
    // Speed boost
    Vector2 currentVelocity = body.linearVelocity;
    body.linearVelocity = currentVelocity * 1.5;
  }
}

/// Black bird - explodes on impact or when ability is used
class BlackBird extends BirdComponent {
  BlackBird({Vector2? position})
      : super(
          birdType: 'black',
          radius: 0.35,
          density: 1.2,
          color: Colors.black,
          position: position,
        );
  
  @override
  void performSpecialAbility() {
    explode();
  }
  
  @override
  void beginContact(Object other, Contact contact) {
    super.beginContact(other, contact);
    
    // Explode on any significant impact
    if (body.linearVelocity.length > 5.0) {
      explode();
    }
  }
  
  void explode() {
    // Create explosion effect
    Vector2 explosionCenter = body.position;
    double explosionRadius = 2.0;
    
    // Find all objects within explosion radius and damage them
    // This would need to be implemented with proper collision detection
    // For now, just destroy the bird
    destroy();
  }
}

/// White bird - drops an egg bomb
class WhiteBird extends BirdComponent {
  WhiteBird({Vector2? position})
      : super(
          birdType: 'white',
          radius: 0.32,
          density: 1.1,
          color: Colors.white,
          position: position,
        );
  
  @override
  void performSpecialAbility() {
    // Drop egg bomb
    Vector2 currentPosition = body.position;
    Vector2 currentVelocity = body.linearVelocity;
    
    // Create egg bomb (simplified as another bird for now)
    BlackBird eggBomb = BlackBird(position: currentPosition + Vector2(0, 0.5));
    eggBomb.isLaunched = true;
    eggBomb.body.linearVelocity = Vector2(currentVelocity.x * 0.5, currentVelocity.y + 2);
    
    parent?.add(eggBomb);
    
    // Bird flies upward after dropping egg
    body.applyLinearImpulse(Vector2(0, -5));
  }
}
