import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

import 'package:angry_birds_game/main.dart';

void main() {
  testWidgets('App launches with main menu', (WidgetTester tester) async {
    // Build our app and trigger a frame.
    await tester.pumpWidget(const AngryBirdsApp());

    // Verify that the main menu is displayed
    expect(find.text('ANGRY BIRDS'), findsOneWidget);
    expect(find.text('PLAY'), findsOneWidget);
    expect(find.text('QUICK PLAY'), findsOneWidget);
    expect(find.text('SETTINGS'), findsOneWidget);
    expect(find.text('ABOUT'), findsOneWidget);
  });

  testWidgets('Level selection screen navigation', (WidgetTester tester) async {
    // Build our app and trigger a frame.
    await tester.pumpWidget(const AngryBirdsApp());

    // Tap the PLAY button
    await tester.tap(find.text('PLAY'));
    await tester.pumpAndSettle();

    // Verify that the level selection screen is displayed
    expect(find.text('SELECT LEVEL'), findsOneWidget);

    // Verify that level buttons are present
    expect(find.text('1'), findsOneWidget);
    expect(find.text('2'), findsOneWidget);
    // Note: Level 10 might not be visible without scrolling
  });

  testWidgets('Settings dialog opens', (WidgetTester tester) async {
    // Build our app and trigger a frame.
    await tester.pumpWidget(const AngryBirdsApp());

    // Tap the SETTINGS button
    await tester.tap(find.text('SETTINGS'));
    await tester.pumpAndSettle();

    // Verify that the settings dialog is displayed
    expect(find.text('Settings'), findsOneWidget);
    expect(find.text('Sound Effects'), findsOneWidget);
    expect(find.text('Background Music'), findsOneWidget);
  });

  testWidgets('About dialog opens', (WidgetTester tester) async {
    // Build our app and trigger a frame.
    await tester.pumpWidget(const AngryBirdsApp());

    // Tap the ABOUT button
    await tester.tap(find.text('ABOUT'));
    await tester.pumpAndSettle();

    // Verify that the about dialog is displayed
    expect(find.text('About'), findsOneWidget);
    expect(find.textContaining('Angry Birds'), findsOneWidget);
  });
}
