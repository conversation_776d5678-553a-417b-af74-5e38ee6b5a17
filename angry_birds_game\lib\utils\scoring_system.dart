/// Scoring system for the Angry Birds game
class ScoringSystem {
  // Base scores for different actions
  static const int pigDestroyedScore = 5000;
  static const int blockDestroyedScore = 500;
  static const int birdUnusedBonus = 10000;
  
  // Material multipliers
  static const Map<String, double> materialMultipliers = {
    'wood': 1.0,
    'stone': 2.0,
    'ice': 0.8,
    'glass': 0.5,
  };
  
  // Pig type multipliers
  static const Map<String, double> pigMultipliers = {
    'basic': 1.0,
    'helmet': 2.0,
    'king': 3.0,
  };
  
  // Combo system
  static const int comboBaseScore = 1000;
  static const double comboMultiplier = 1.5;
  
  /// Calculate score for destroying a pig
  static int calculatePigScore(String pigType, {int comboCount = 0}) {
    double multiplier = pigMultipliers[pigType] ?? 1.0;
    int baseScore = (pigDestroyedScore * multiplier).round();
    
    // Apply combo bonus
    if (comboCount > 1) {
      double comboBonus = comboBaseScore * (comboCount - 1) * comboMultiplier;
      baseScore += comboBonus.round();
    }
    
    return baseScore;
  }
  
  /// Calculate score for destroying a block
  static int calculateBlockScore(String blockType, {int comboCount = 0}) {
    double multiplier = materialMultipliers[blockType] ?? 1.0;
    int baseScore = (blockDestroyedScore * multiplier).round();
    
    // Apply combo bonus
    if (comboCount > 1) {
      double comboBonus = (comboBaseScore * 0.5) * (comboCount - 1) * comboMultiplier;
      baseScore += comboBonus.round();
    }
    
    return baseScore;
  }
  
  /// Calculate bonus for unused birds
  static int calculateBirdBonus(int unusedBirds) {
    return unusedBirds * birdUnusedBonus;
  }
  
  /// Calculate star rating based on score and thresholds
  static int calculateStars(int score, int oneStarThreshold, int twoStarThreshold, int threeStarThreshold) {
    if (score >= threeStarThreshold) return 3;
    if (score >= twoStarThreshold) return 2;
    if (score >= oneStarThreshold) return 1;
    return 0;
  }
  
  /// Calculate destruction percentage bonus
  static int calculateDestructionBonus(double destructionPercentage) {
    if (destructionPercentage >= 1.0) {
      return 5000; // Perfect destruction bonus
    } else if (destructionPercentage >= 0.8) {
      return 2000; // High destruction bonus
    } else if (destructionPercentage >= 0.6) {
      return 1000; // Medium destruction bonus
    }
    return 0;
  }
  
  /// Calculate time bonus (if level completed quickly)
  static int calculateTimeBonus(double timeElapsed, double targetTime) {
    if (timeElapsed <= targetTime) {
      double ratio = (targetTime - timeElapsed) / targetTime;
      return (ratio * 3000).round(); // Up to 3000 points for speed
    }
    return 0;
  }
  
  /// Calculate total level score
  static LevelScore calculateLevelScore({
    required int pigScore,
    required int blockScore,
    required int unusedBirds,
    required double destructionPercentage,
    required double timeElapsed,
    required double targetTime,
    required int oneStarThreshold,
    required int twoStarThreshold,
    required int threeStarThreshold,
  }) {
    int birdBonus = calculateBirdBonus(unusedBirds);
    int destructionBonus = calculateDestructionBonus(destructionPercentage);
    int timeBonus = calculateTimeBonus(timeElapsed, targetTime);
    
    int totalScore = pigScore + blockScore + birdBonus + destructionBonus + timeBonus;
    int stars = calculateStars(totalScore, oneStarThreshold, twoStarThreshold, threeStarThreshold);
    
    return LevelScore(
      pigScore: pigScore,
      blockScore: blockScore,
      birdBonus: birdBonus,
      destructionBonus: destructionBonus,
      timeBonus: timeBonus,
      totalScore: totalScore,
      stars: stars,
      destructionPercentage: destructionPercentage,
    );
  }
}

/// Data class for level score breakdown
class LevelScore {
  final int pigScore;
  final int blockScore;
  final int birdBonus;
  final int destructionBonus;
  final int timeBonus;
  final int totalScore;
  final int stars;
  final double destructionPercentage;
  
  LevelScore({
    required this.pigScore,
    required this.blockScore,
    required this.birdBonus,
    required this.destructionBonus,
    required this.timeBonus,
    required this.totalScore,
    required this.stars,
    required this.destructionPercentage,
  });
  
  /// Get score breakdown as a map
  Map<String, dynamic> toMap() {
    return {
      'pigScore': pigScore,
      'blockScore': blockScore,
      'birdBonus': birdBonus,
      'destructionBonus': destructionBonus,
      'timeBonus': timeBonus,
      'totalScore': totalScore,
      'stars': stars,
      'destructionPercentage': destructionPercentage,
    };
  }
  
  @override
  String toString() {
    return 'LevelScore(total: $totalScore, stars: $stars, pigs: $pigScore, blocks: $blockScore, birds: $birdBonus, destruction: $destructionBonus, time: $timeBonus)';
  }
}

/// Combo tracker for consecutive destructions
class ComboTracker {
  int _currentCombo = 0;
  double _lastDestructionTime = 0;
  static const double comboTimeWindow = 2.0; // 2 seconds to maintain combo
  
  /// Add a destruction to the combo
  void addDestruction(double currentTime) {
    if (currentTime - _lastDestructionTime <= comboTimeWindow) {
      _currentCombo++;
    } else {
      _currentCombo = 1; // Reset combo
    }
    _lastDestructionTime = currentTime;
  }
  
  /// Get current combo count
  int getCurrentCombo() => _currentCombo;
  
  /// Reset combo
  void resetCombo() {
    _currentCombo = 0;
    _lastDestructionTime = 0;
  }
  
  /// Check if combo is active
  bool isComboActive(double currentTime) {
    return currentTime - _lastDestructionTime <= comboTimeWindow;
  }
}

/// Achievement system
class AchievementSystem {
  static const Map<String, Achievement> achievements = {
    'first_blood': Achievement(
      id: 'first_blood',
      name: 'First Blood',
      description: 'Destroy your first pig',
      points: 100,
    ),
    'combo_master': Achievement(
      id: 'combo_master',
      name: 'Combo Master',
      description: 'Achieve a 5x combo',
      points: 500,
    ),
    'perfect_level': Achievement(
      id: 'perfect_level',
      name: 'Perfect Level',
      description: 'Complete a level with 100% destruction',
      points: 1000,
    ),
    'speed_demon': Achievement(
      id: 'speed_demon',
      name: 'Speed Demon',
      description: 'Complete a level in under 30 seconds',
      points: 750,
    ),
    'bird_saver': Achievement(
      id: 'bird_saver',
      name: 'Bird Saver',
      description: 'Complete a level using only one bird',
      points: 2000,
    ),
  };
  
  /// Check if an achievement should be unlocked
  static List<Achievement> checkAchievements(LevelScore score, double timeElapsed, int birdsUsed) {
    List<Achievement> unlockedAchievements = [];
    
    // Perfect level achievement
    if (score.destructionPercentage >= 1.0) {
      unlockedAchievements.add(achievements['perfect_level']!);
    }
    
    // Speed demon achievement
    if (timeElapsed <= 30.0) {
      unlockedAchievements.add(achievements['speed_demon']!);
    }
    
    // Bird saver achievement
    if (birdsUsed == 1) {
      unlockedAchievements.add(achievements['bird_saver']!);
    }
    
    return unlockedAchievements;
  }
}

/// Achievement data class
class Achievement {
  final String id;
  final String name;
  final String description;
  final int points;
  
  const Achievement({
    required this.id,
    required this.name,
    required this.description,
    required this.points,
  });
}
