import 'package:flutter/material.dart';

import 'main_menu.dart';

/// Level selection screen
class LevelSelectionScreen extends StatelessWidget {
  const LevelSelectionScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Color(0xFF87CEEB), // Sky blue
              Color(0xFF98FB98), // Pale green
            ],
          ),
        ),
        child: Safe<PERSON>rea(
          child: Column(
            children: [
              // Header
              Padding(
                padding: const EdgeInsets.all(20),
                child: Row(
                  children: [
                    IconButton(
                      onPressed: () => Navigator.pop(context),
                      icon: const Icon(
                        Icons.arrow_back,
                        color: Colors.white,
                        size: 30,
                      ),
                      style: IconButton.styleFrom(
                        backgroundColor: Colors.black54,
                        shape: const CircleBorder(),
                      ),
                    ),
                    const Expanded(
                      child: Text(
                        'SELECT LEVEL',
                        textAlign: TextAlign.center,
                        style: TextStyle(
                          fontSize: 28,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                          shadows: [
                            Shadow(
                              offset: Offset(2, 2),
                              blurRadius: 4,
                              color: Colors.black54,
                            ),
                          ],
                        ),
                      ),
                    ),
                    const SizedBox(width: 50), // Balance the back button
                  ],
                ),
              ),
              
              // Level Grid
              Expanded(
                child: Padding(
                  padding: const EdgeInsets.all(20),
                  child: GridView.builder(
                    gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                      crossAxisCount: 3,
                      crossAxisSpacing: 15,
                      mainAxisSpacing: 15,
                      childAspectRatio: 1.0,
                    ),
                    itemCount: 10,
                    itemBuilder: (context, index) {
                      int levelNumber = index + 1;
                      return _buildLevelButton(context, levelNumber);
                    },
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildLevelButton(BuildContext context, int levelNumber) {
    // For now, all levels are unlocked
    bool isUnlocked = true;
    int stars = _getLevelStars(levelNumber);
    
    return GestureDetector(
      onTap: isUnlocked ? () => _startLevel(context, levelNumber) : null,
      child: Container(
        decoration: BoxDecoration(
          color: isUnlocked ? Colors.green[400] : Colors.grey[400],
          borderRadius: BorderRadius.circular(15),
          border: Border.all(
            color: Colors.white,
            width: 3,
          ),
          boxShadow: [
            BoxShadow(
              offset: const Offset(0, 4),
              blurRadius: 8,
              color: Colors.black.withOpacity(0.3),
            ),
          ],
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // Level number
            Text(
              '$levelNumber',
              style: TextStyle(
                fontSize: 32,
                fontWeight: FontWeight.bold,
                color: isUnlocked ? Colors.white : Colors.grey[600],
              ),
            ),
            const SizedBox(height: 5),
            
            // Stars
            if (isUnlocked) _buildStars(stars),
            
            // Lock icon for locked levels
            if (!isUnlocked)
              Icon(
                Icons.lock,
                color: Colors.grey[600],
                size: 24,
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildStars(int starCount) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: List.generate(3, (index) {
        return Icon(
          index < starCount ? Icons.star : Icons.star_border,
          color: index < starCount ? Colors.yellow[600] : Colors.white,
          size: 16,
        );
      }),
    );
  }

  int _getLevelStars(int levelNumber) {
    // For demo purposes, return random stars
    // In a real game, this would come from saved progress
    switch (levelNumber) {
      case 1:
        return 3;
      case 2:
        return 2;
      case 3:
        return 1;
      case 4:
        return 3;
      case 5:
        return 0;
      default:
        return 0;
    }
  }

  void _startLevel(BuildContext context, int levelNumber) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => GameScreen(levelNumber: levelNumber),
      ),
    );
  }
}

/// Level info dialog
class LevelInfoDialog extends StatelessWidget {
  final int levelNumber;
  final String levelName;
  final String description;
  final int stars;

  const LevelInfoDialog({
    super.key,
    required this.levelNumber,
    required this.levelName,
    required this.description,
    required this.stars,
  });

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text('Level $levelNumber'),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            levelName,
            style: const TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 10),
          Text(description),
          const SizedBox(height: 15),
          Row(
            children: [
              const Text('Best Score: '),
              Row(
                children: List.generate(3, (index) {
                  return Icon(
                    index < stars ? Icons.star : Icons.star_border,
                    color: index < stars ? Colors.yellow[600] : Colors.grey,
                    size: 20,
                  );
                }),
              ),
            ],
          ),
        ],
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: const Text('Cancel'),
        ),
        ElevatedButton(
          onPressed: () {
            Navigator.pop(context);
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => GameScreen(levelNumber: levelNumber),
              ),
            );
          },
          child: const Text('Play'),
        ),
      ],
    );
  }
}
