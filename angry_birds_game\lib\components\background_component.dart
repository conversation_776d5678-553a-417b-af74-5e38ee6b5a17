import 'package:flame/components.dart';
import 'package:flutter/material.dart';

/// Background component that renders the game background
class BackgroundComponent extends Component {
  @override
  void render(Canvas canvas) {
    super.render(canvas);
    
    // Create gradient background
    final rect = Rect.fromLTWH(-1000, -1000, 2000, 2000);
    
    final gradient = LinearGradient(
      begin: Alignment.topCenter,
      end: Alignment.bottomCenter,
      colors: [
        Colors.lightBlue[300]!,
        Colors.lightBlue[100]!,
      ],
    );
    
    final paint = Paint()
      ..shader = gradient.createShader(rect);
    
    canvas.drawRect(rect, paint);
    
    // Draw simple clouds
    _drawClouds(canvas);
  }
  
  void _drawClouds(Canvas canvas) {
    final cloudPaint = Paint()
      ..color = Colors.white.withOpacity(0.8)
      ..style = PaintingStyle.fill;
    
    // Cloud positions
    final cloudPositions = [
      Vector2(-8, -6),
      Vector2(-2, -7),
      Vector2(5, -6),
      Vector2(12, -8),
    ];
    
    for (Vector2 pos in cloudPositions) {
      _drawCloud(canvas, pos, cloudPaint);
    }
  }
  
  void _drawCloud(Canvas canvas, Vector2 position, Paint paint) {
    // Draw multiple circles to form a cloud
    final cloudCircles = [
      Vector2(0, 0),
      Vector2(0.5, -0.2),
      Vector2(-0.5, -0.2),
      Vector2(0.3, 0.3),
      Vector2(-0.3, 0.3),
    ];
    
    for (Vector2 circlePos in cloudCircles) {
      canvas.drawCircle(
        Offset(position.x + circlePos.x, position.y + circlePos.y),
        0.4,
        paint,
      );
    }
  }
}
