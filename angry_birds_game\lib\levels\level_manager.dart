import 'dart:convert';
import 'package:flutter/services.dart';
import 'package:flutter/foundation.dart';
import 'package:flame/components.dart';

import '../game/angry_birds_game.dart';
import '../components/bird_component.dart';
import '../components/pig_component.dart';
import '../components/block_component.dart';
import 'level_data.dart';

/// Manages level loading, progression, and scoring
class LevelManager {
  final AngryBirdsGame game;
  
  // Current level data
  LevelData? _currentLevel;
  int _totalLevels = 10;
  
  // Scoring
  int _destructionScore = 0;
  int _remainingBirds = 0;
  
  // Level completion tracking
  bool _levelCompleted = false;
  bool _levelFailed = false;

  LevelManager(this.game);

  // Getters
  LevelData? get currentLevel => _currentLevel;
  int get totalLevels => _totalLevels;
  
  /// Load a specific level
  Future<void> loadLevel(int levelNumber) async {
    try {
      // Reset level state
      _resetLevelState();
      
      // Load level data
      _currentLevel = await _loadLevelData(levelNumber);
      
      if (_currentLevel != null) {
        // Spawn level objects
        await _spawnLevelObjects();
        
        debugPrint('Level $levelNumber loaded successfully');
      } else {
        debugPrint('Failed to load level $levelNumber');
      }
    } catch (e) {
      debugPrint('Error loading level $levelNumber: $e');
    }
  }
  
  /// Load level data from JSON file
  Future<LevelData?> _loadLevelData(int levelNumber) async {
    try {
      String jsonString = await rootBundle.loadString('assets/levels/level_$levelNumber.json');
      Map<String, dynamic> jsonData = json.decode(jsonString);
      return LevelData.fromJson(jsonData);
    } catch (e) {
      // If level file doesn't exist, generate a default level
      debugPrint('Level file not found, generating default level: $e');
      return _generateDefaultLevel(levelNumber);
    }
  }
  
  /// Generate a default level if JSON file doesn't exist
  LevelData _generateDefaultLevel(int levelNumber) {
    return LevelData(
      levelNumber: levelNumber,
      name: 'Level $levelNumber',
      description: 'Default generated level',
      birds: _getDefaultBirds(levelNumber),
      pigs: _getDefaultPigs(levelNumber),
      blocks: _getDefaultBlocks(levelNumber),
      oneStarScore: 10000,
      twoStarScore: 20000,
      threeStarScore: 30000,
    );
  }
  
  /// Get default birds for a level
  List<BirdData> _getDefaultBirds(int levelNumber) {
    List<BirdData> birds = [];
    
    // Add birds based on level number
    int birdCount = (levelNumber / 2).ceil().clamp(1, 5);
    
    for (int i = 0; i < birdCount; i++) {
      birds.add(BirdData(
        type: i % 3 == 0 ? 'red' : (i % 3 == 1 ? 'blue' : 'yellow'),
        position: {'x': -8.0 - (i * 0.5), 'y': -2.0},
      ));
    }
    
    return birds;
  }
  
  /// Get default pigs for a level
  List<PigData> _getDefaultPigs(int levelNumber) {
    List<PigData> pigs = [];
    
    // Add pigs based on level number
    int pigCount = (levelNumber / 3).ceil().clamp(1, 4);
    
    for (int i = 0; i < pigCount; i++) {
      pigs.add(PigData(
        type: 'basic',
        position: {'x': 5.0 + (i * 2.0), 'y': -2.0},
        health: 100,
      ));
    }
    
    return pigs;
  }
  
  /// Get default blocks for a level
  List<BlockData> _getDefaultBlocks(int levelNumber) {
    List<BlockData> blocks = [];
    
    // Create a simple structure
    double baseX = 4.0;
    double baseY = -2.0;
    
    // Add some blocks around pigs
    for (int i = 0; i < levelNumber.clamp(3, 8); i++) {
      blocks.add(BlockData(
        type: i % 2 == 0 ? 'wood' : 'stone',
        position: {'x': baseX + (i * 1.0), 'y': baseY + (i % 3) * 1.0},
        rotation: 0.0,
      ));
    }
    
    return blocks;
  }
  
  /// Spawn all level objects in the game world
  Future<void> _spawnLevelObjects() async {
    if (_currentLevel == null) return;
    
    // Clear existing objects
    game.worldComponent.clearDynamicObjects();
    
    // Spawn birds (they'll be queued in the slingshot)
    _remainingBirds = _currentLevel!.birds.length;
    _spawnBirds();

    // Spawn pigs
    _spawnPigs();

    // Spawn blocks
    _spawnBlocks();
  }
  
  /// Spawn birds for the level
  void _spawnBirds() {
    if (_currentLevel == null) return;

    List<BirdComponent> birds = [];

    for (BirdData birdData in _currentLevel!.birds) {
      BirdComponent bird = _createBird(birdData);
      birds.add(bird);
    }

    // Add birds to slingshot queue
    game.worldComponent.slingshot.addBirds(birds);
  }

  /// Spawn pigs for the level
  void _spawnPigs() {
    if (_currentLevel == null) return;

    for (PigData pigData in _currentLevel!.pigs) {
      PigComponent pig = _createPig(pigData);
      game.worldComponent.addPig(pig);
    }
  }

  /// Spawn blocks for the level
  void _spawnBlocks() {
    if (_currentLevel == null) return;

    for (BlockData blockData in _currentLevel!.blocks) {
      BlockComponent block = _createBlock(blockData);
      game.worldComponent.addBlock(block);
    }
  }

  /// Create a bird from bird data
  BirdComponent _createBird(BirdData birdData) {
    Vector2 position = Vector2(birdData.x, birdData.y);

    switch (birdData.type) {
      case 'red':
        return RedBird(position: position);
      case 'blue':
        return BlueBird(position: position);
      case 'yellow':
        return YellowBird(position: position);
      case 'black':
        return BlackBird(position: position);
      case 'white':
        return WhiteBird(position: position);
      default:
        return RedBird(position: position);
    }
  }

  /// Create a pig from pig data
  PigComponent _createPig(PigData pigData) {
    Vector2 position = Vector2(pigData.x, pigData.y);

    switch (pigData.type) {
      case 'basic':
        return BasicPig(position: position);
      case 'helmet':
        return HelmetPig(position: position);
      case 'king':
        return KingPig(position: position);
      default:
        return BasicPig(position: position);
    }
  }

  /// Create a block from block data
  BlockComponent _createBlock(BlockData blockData) {
    Vector2 position = Vector2(blockData.x, blockData.y);

    switch (blockData.type) {
      case 'wood':
        return WoodBlock(position: position, rotation: blockData.rotation);
      case 'stone':
        return StoneBlock(position: position, rotation: blockData.rotation);
      case 'ice':
        return IceBlock(position: position, rotation: blockData.rotation);
      case 'glass':
        return GlassBlock(position: position, rotation: blockData.rotation);
      default:
        return WoodBlock(position: position, rotation: blockData.rotation);
    }
  }

  /// Reset level state
  void _resetLevelState() {
    _destructionScore = 0;
    _remainingBirds = 0;
    _levelCompleted = false;
    _levelFailed = false;
  }
  
  /// Check if level is completed (all pigs destroyed)
  bool isLevelCompleted() {
    if (_currentLevel == null) return false;
    
    // Check if all pigs are destroyed
    bool allPigsDestroyed = game.worldComponent.areAllPigsDestroyed();
    
    if (allPigsDestroyed && !_levelCompleted) {
      _levelCompleted = true;
    }
    
    return _levelCompleted;
  }
  
  /// Check if level is failed (no birds left and pigs still alive)
  bool isLevelFailed() {
    if (_currentLevel == null) return false;
    
    // Check if no birds left and pigs still alive
    bool noBirdsLeft = _remainingBirds <= 0;
    bool pigsAlive = !game.worldComponent.areAllPigsDestroyed();
    
    if (noBirdsLeft && pigsAlive && !_levelFailed) {
      _levelFailed = true;
    }
    
    return _levelFailed;
  }
  
  /// Update level manager
  void update(double dt) {
    // Update level-specific logic
    if (_currentLevel != null) {
      // Check for level completion/failure
      isLevelCompleted();
      isLevelFailed();
    }
  }
  
  /// Get destruction score
  int getDestructionScore() {
    return _destructionScore;
  }
  
  /// Add to destruction score
  void addDestructionScore(int points) {
    _destructionScore += points;
  }
  
  /// Get remaining birds bonus
  int getRemainingBirdsBonus() {
    return _remainingBirds * 10000; // 10k points per remaining bird
  }
  
  /// Use a bird
  void useBird() {
    if (_remainingBirds > 0) {
      _remainingBirds--;
    }
  }
  
  /// Get score thresholds
  int getOneStarScore() => _currentLevel?.oneStarScore ?? 10000;
  int getTwoStarScore() => _currentLevel?.twoStarScore ?? 20000;
  int getThreeStarScore() => _currentLevel?.threeStarScore ?? 30000;
  
  /// Get level progress (0.0 to 1.0)
  double getLevelProgress() {
    if (_currentLevel == null) return 0.0;
    
    int totalPigs = _currentLevel!.pigs.length;
    int remainingPigs = game.worldComponent.getPigs().length;
    
    if (totalPigs == 0) return 1.0;
    
    return (totalPigs - remainingPigs) / totalPigs;
  }
}
