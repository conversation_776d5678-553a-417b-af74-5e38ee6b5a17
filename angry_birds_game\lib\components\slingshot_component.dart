import 'package:flame/components.dart';
import 'package:flame_forge2d/flame_forge2d.dart';
import 'package:flutter/material.dart';

import 'bird_component.dart';

/// Slingshot component for launching birds
class SlingshotComponent extends BodyComponent {
  late Vector2 basePosition;
  Vector2? dragPosition;
  bool isDragging = false;

  // Bird queue
  List<BirdComponent> birdQueue = [];
  BirdComponent? currentBird;

  // Slingshot properties
  static const double maxStretch = 3.0;
  static const double launchForceMultiplier = 10.0;
  
  SlingshotComponent({Vector2? position}) {
    basePosition = position ?? Vector2.zero();
  }
  
  @override
  Body createBody() {
    final bodyDef = BodyDef(
      type: BodyType.static,
      position: basePosition,
    );
    
    final body = world.createBody(bodyDef);
    
    // Create slingshot fixture (for collision detection)
    final shape = CircleShape()..radius = 0.5;
    final fixtureDef = FixtureDef(shape)
      ..isSensor = true;
    
    body.createFixture(fixtureDef);
    
    return body;
  }
  

  
  /// Handle tap on slingshot
  void handleTap(Vector2 worldPosition) {
    if (_isWithinSlingshotRange(worldPosition)) {
      isDragging = true;
      dragPosition = worldPosition;
    }
  }
  
  /// Handle pan/drag
  void handlePan(Vector2 worldPosition) {
    if (isDragging) {
      // Limit drag distance
      Vector2 dragVector = worldPosition - basePosition;
      if (dragVector.length > maxStretch) {
        dragVector = dragVector.normalized() * maxStretch;
      }
      
      dragPosition = basePosition + dragVector;
    }
  }
  
  /// Handle pan end (release)
  void handlePanEnd() {
    if (isDragging && dragPosition != null) {
      // Calculate launch force
      Vector2 launchVector = basePosition - dragPosition!;
      Vector2 launchForce = launchVector * launchForceMultiplier;
      
      // Launch bird
      _launchBird(launchForce);
      
      // Reset drag state
      isDragging = false;
      dragPosition = null;
    }
  }
  
  /// Check if position is within slingshot interaction range
  bool _isWithinSlingshotRange(Vector2 worldPosition) {
    return (worldPosition - basePosition).length <= maxStretch;
  }
  
  /// Launch a bird with the given force
  void _launchBird(Vector2 force) {
    if (currentBird != null) {
      currentBird!.launch(force);

      // Play launch sound (placeholder - would need audio manager reference)
      // audioManager.playSound('bird_launch');

      currentBird = null;

      // Load next bird
      _loadNextBird();
    }
  }

  /// Add birds to the queue
  void addBirds(List<BirdComponent> birds) {
    birdQueue.addAll(birds);
    if (currentBird == null) {
      _loadNextBird();
    }
  }

  /// Load the next bird from the queue
  void _loadNextBird() {
    if (birdQueue.isNotEmpty) {
      currentBird = birdQueue.removeAt(0);
      parent?.add(currentBird!);
      // Position will be set when the bird's body is created
      currentBird!.body.setTransform(basePosition, 0);
    }
  }

  /// Check if there are birds remaining
  bool hasBirdsRemaining() {
    return currentBird != null || birdQueue.isNotEmpty;
  }

  /// Get number of remaining birds
  int getRemainingBirdsCount() {
    int count = birdQueue.length;
    if (currentBird != null) count++;
    return count;
  }

  /// Check if point is within slingshot bounds
  @override
  bool containsPoint(Vector2 point) {
    return (point - basePosition).length <= 1.0;
  }
  
  @override
  void render(Canvas canvas) {
    super.render(canvas);
    
    // Draw slingshot base
    final paint = Paint()
      ..color = Colors.brown
      ..style = PaintingStyle.fill;
    
    canvas.drawCircle(
      Offset.zero,
      0.5,
      paint,
    );
    
    // Draw slingshot arms
    final armPaint = Paint()
      ..color = Colors.brown
      ..strokeWidth = 0.1
      ..style = PaintingStyle.stroke;
    
    canvas.drawLine(
      const Offset(-0.3, 0),
      const Offset(-0.2, -1.0),
      armPaint,
    );
    
    canvas.drawLine(
      const Offset(0.3, 0),
      const Offset(0.2, -1.0),
      armPaint,
    );
    
    // Draw rubber band if dragging
    if (isDragging && dragPosition != null) {
      final rubberPaint = Paint()
        ..color = Colors.black
        ..strokeWidth = 0.05
        ..style = PaintingStyle.stroke;
      
      Vector2 localDragPos = dragPosition! - position;
      
      canvas.drawLine(
        const Offset(-0.2, -1.0),
        Offset(localDragPos.x, localDragPos.y),
        rubberPaint,
      );
      
      canvas.drawLine(
        const Offset(0.2, -1.0),
        Offset(localDragPos.x, localDragPos.y),
        rubberPaint,
      );
    }
  }
}
