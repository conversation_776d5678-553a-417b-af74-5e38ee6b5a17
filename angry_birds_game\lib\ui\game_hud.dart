import 'package:flame/components.dart';
import 'package:flutter/material.dart';

import '../game/angry_birds_game.dart';

/// Game HUD component that displays UI elements
class GameHUD extends Component with HasGameRef<AngryBirdsGame> {
  final AngryBirdsGame gameInstance;

  GameHUD(this.gameInstance);
  late TextComponent levelText;
  late TextComponent scoreText;
  late TextComponent birdsText;
  
  // UI state
  int currentLevel = 1;
  int currentScore = 0;
  int remainingBirds = 0;
  
  @override
  Future<void> onLoad() async {
    super.onLoad();
    
    // Create UI components
    await _createUIComponents();
  }
  
  Future<void> _createUIComponents() async {
    // Level text
    levelText = TextComponent(
      text: 'Level $currentLevel',
      textRenderer: TextPaint(
        style: const TextStyle(
          color: Colors.white,
          fontSize: 24,
          fontWeight: FontWeight.bold,
        ),
      ),
      position: Vector2(20, 20),
    );
    add(levelText);
    
    // Score text
    scoreText = TextComponent(
      text: 'Score: $currentScore',
      textRenderer: TextPaint(
        style: const TextStyle(
          color: Colors.white,
          fontSize: 20,
        ),
      ),
      position: Vector2(20, 50),
    );
    add(scoreText);
    
    // Birds remaining text
    birdsText = TextComponent(
      text: 'Birds: $remainingBirds',
      textRenderer: TextPaint(
        style: const TextStyle(
          color: Colors.white,
          fontSize: 20,
        ),
      ),
      position: Vector2(20, 80),
    );
    add(birdsText);
  }
  
  /// Update level display
  void updateLevel(int level) {
    currentLevel = level;
    levelText.text = 'Level $level';
  }
  
  /// Update score display
  void updateScore(int score) {
    currentScore = score;
    scoreText.text = 'Score: $score';
  }
  
  /// Update birds remaining display
  void updateBirds(int birds) {
    remainingBirds = birds;
    birdsText.text = 'Birds: $birds';
  }
  
  /// Show level complete dialog
  void showLevelComplete(int score, int stars) {
    // TODO: Implement level complete overlay
    print('Level Complete! Score: $score, Stars: $stars');
  }
  
  /// Show level failed dialog
  void showLevelFailed() {
    // TODO: Implement level failed overlay
    print('Level Failed!');
  }
  
  @override
  void update(double dt) {
    super.update(dt);
    
    // Update HUD elements based on game state
    if (gameRef.gameState != null) {
      updateScore(gameRef.gameState.score);
      updateLevel(gameRef.gameState.currentLevel);
    }
  }
}
