import 'package:flame/components.dart';
import 'package:flutter/material.dart';

import '../game/angry_birds_game.dart';
import 'game_overlays.dart';

/// Game HUD component that displays UI elements
class GameHUD extends Component with HasGameRef<AngryBirdsGame> {
  final AngryBirdsGame gameInstance;

  GameHUD(this.gameInstance);
  late TextComponent levelText;
  late TextComponent scoreText;
  late TextComponent birdsText;
  
  // UI state
  int currentLevel = 1;
  int currentScore = 0;
  int remainingBirds = 0;
  
  @override
  Future<void> onLoad() async {
    super.onLoad();
    
    // Create UI components
    await _createUIComponents();
  }
  
  Future<void> _createUIComponents() async {
    // Level text
    levelText = TextComponent(
      text: 'Level $currentLevel',
      textRenderer: TextPaint(
        style: const TextStyle(
          color: Colors.white,
          fontSize: 24,
          fontWeight: FontWeight.bold,
        ),
      ),
      position: Vector2(20, 20),
    );
    add(levelText);
    
    // Score text
    scoreText = TextComponent(
      text: 'Score: $currentScore',
      textRenderer: TextPaint(
        style: const TextStyle(
          color: Colors.white,
          fontSize: 20,
        ),
      ),
      position: Vector2(20, 50),
    );
    add(scoreText);
    
    // Birds remaining text
    birdsText = TextComponent(
      text: 'Birds: $remainingBirds',
      textRenderer: TextPaint(
        style: const TextStyle(
          color: Colors.white,
          fontSize: 20,
        ),
      ),
      position: Vector2(20, 80),
    );
    add(birdsText);
  }
  
  /// Update level display
  void updateLevel(int level) {
    currentLevel = level;
    levelText.text = 'Level $level';
  }
  
  /// Update score display
  void updateScore(int score) {
    currentScore = score;
    scoreText.text = 'Score: $score';
  }
  
  /// Update birds remaining display
  void updateBirds(int birds) {
    remainingBirds = birds;
    birdsText.text = 'Birds: $birds';
  }
  
  /// Show level complete dialog
  void showLevelComplete(int score, int stars) {
    // Create level complete overlay
    final overlay = LevelCompleteOverlay(
      score: score,
      stars: stars,
      onNextLevel: () => gameInstance.nextLevel(),
      onRestart: () => gameInstance.restartLevel(),
      onMenu: () {
        // Navigate back to menu
      },
    );

    add(overlay);
  }

  /// Show level failed dialog
  void showLevelFailed() {
    // Create level failed overlay
    final overlay = LevelFailedOverlay(
      onRestart: () => gameInstance.restartLevel(),
      onMenu: () {
        // Navigate back to menu
      },
    );

    add(overlay);
  }
  
  @override
  void update(double dt) {
    super.update(dt);
    
    // Update HUD elements based on game state
    if (gameRef.gameState != null) {
      updateScore(gameRef.gameState.score);
      updateLevel(gameRef.gameState.currentLevel);
    }
  }
}
