import 'package:flame_audio/flame_audio.dart';
import 'package:flutter/foundation.dart';

/// Audio manager for handling all game sounds and music
class AudioManager {
  // Audio settings
  bool _soundEnabled = true;
  bool _musicEnabled = true;
  double _soundVolume = 1.0;
  double _musicVolume = 0.7;
  
  // Background music
  String? _currentBackgroundMusic;
  
  // Sound effect file mappings
  static const Map<String, String> _soundFiles = {
    'bird_launch': 'sounds/bird_launch.wav',
    'bird_hit': 'sounds/bird_hit.wav',
    'pig_destroy': 'sounds/pig_destroy.wav',
    'block_break': 'sounds/block_break.wav',
    'slingshot_stretch': 'sounds/slingshot_stretch.wav',
    'level_complete': 'sounds/level_complete.wav',
    'level_failed': 'sounds/level_failed.wav',
    'button_click': 'sounds/button_click.wav',
    'restart': 'sounds/restart.wav',
    'explosion': 'sounds/explosion.wav',
  };
  
  // Background music files
  static const Map<String, String> _musicFiles = {
    'menu': 'music/menu_theme.mp3',
    'game': 'music/game_theme.mp3',
    'victory': 'music/victory_theme.mp3',
  };
  
  // Getters
  bool get soundEnabled => _soundEnabled;
  bool get musicEnabled => _musicEnabled;
  double get soundVolume => _soundVolume;
  double get musicVolume => _musicVolume;
  
  /// Initialize the audio manager
  Future<void> initialize() async {
    try {
      // Preload sound effects
      await _preloadSounds();
      
      // Preload music
      await _preloadMusic();
      
      debugPrint('Audio Manager initialized successfully');
    } catch (e) {
      debugPrint('Error initializing Audio Manager: $e');
    }
  }
  
  /// Preload all sound effects
  Future<void> _preloadSounds() async {
    for (String soundFile in _soundFiles.values) {
      try {
        await FlameAudio.audioCache.load(soundFile);
      } catch (e) {
        debugPrint('Failed to preload sound: $soundFile - $e');
      }
    }
  }
  
  /// Preload all music files
  Future<void> _preloadMusic() async {
    for (String musicFile in _musicFiles.values) {
      try {
        await FlameAudio.audioCache.load(musicFile);
      } catch (e) {
        debugPrint('Failed to preload music: $musicFile - $e');
      }
    }
  }
  
  /// Play a sound effect
  void playSound(String soundName, {double? volume}) {
    if (!_soundEnabled) return;
    
    String? soundFile = _soundFiles[soundName];
    if (soundFile != null) {
      try {
        FlameAudio.play(
          soundFile,
          volume: volume ?? _soundVolume,
        );
      } catch (e) {
        debugPrint('Error playing sound $soundName: $e');
      }
    } else {
      debugPrint('Sound not found: $soundName');
    }
  }
  
  /// Play background music
  void playBackgroundMusic(String musicName, {bool loop = true}) {
    if (!_musicEnabled) return;
    
    String? musicFile = _musicFiles[musicName];
    if (musicFile != null) {
      try {
        // Stop current music if playing
        if (_currentBackgroundMusic != null) {
          FlameAudio.bgm.stop();
        }
        
        // Play new music
        FlameAudio.bgm.play(musicFile, volume: _musicVolume);
        _currentBackgroundMusic = musicName;
      } catch (e) {
        debugPrint('Error playing background music $musicName: $e');
      }
    } else {
      debugPrint('Music not found: $musicName');
    }
  }
  
  /// Stop background music
  void stopBackgroundMusic() {
    try {
      FlameAudio.bgm.stop();
      _currentBackgroundMusic = null;
    } catch (e) {
      debugPrint('Error stopping background music: $e');
    }
  }
  
  /// Pause background music
  void pauseBackgroundMusic() {
    try {
      FlameAudio.bgm.pause();
    } catch (e) {
      debugPrint('Error pausing background music: $e');
    }
  }
  
  /// Resume background music
  void resumeBackgroundMusic() {
    try {
      FlameAudio.bgm.resume();
    } catch (e) {
      debugPrint('Error resuming background music: $e');
    }
  }
  
  /// Set sound enabled/disabled
  void setSoundEnabled(bool enabled) {
    _soundEnabled = enabled;
  }
  
  /// Set music enabled/disabled
  void setMusicEnabled(bool enabled) {
    _musicEnabled = enabled;
    
    if (!enabled && _currentBackgroundMusic != null) {
      stopBackgroundMusic();
    }
  }
  
  /// Set sound volume (0.0 to 1.0)
  void setSoundVolume(double volume) {
    _soundVolume = volume.clamp(0.0, 1.0);
  }
  
  /// Set music volume (0.0 to 1.0)
  void setMusicVolume(double volume) {
    _musicVolume = volume.clamp(0.0, 1.0);
    
    // Update current music volume
    if (_currentBackgroundMusic != null) {
      try {
        FlameAudio.bgm.audioPlayer.setVolume(_musicVolume);
      } catch (e) {
        debugPrint('Error setting music volume: $e');
      }
    }
  }
  
  /// Play multiple sounds in sequence
  Future<void> playSoundSequence(List<String> soundNames, {Duration delay = const Duration(milliseconds: 100)}) async {
    for (String soundName in soundNames) {
      playSound(soundName);
      await Future.delayed(delay);
    }
  }
  
  /// Play random sound from a list
  void playRandomSound(List<String> soundNames) {
    if (soundNames.isNotEmpty) {
      int randomIndex = DateTime.now().millisecondsSinceEpoch % soundNames.length;
      playSound(soundNames[randomIndex]);
    }
  }
  
  /// Dispose of audio resources
  void dispose() {
    try {
      stopBackgroundMusic();
      FlameAudio.audioCache.clearAll();
    } catch (e) {
      debugPrint('Error disposing audio manager: $e');
    }
  }
  
  /// Get audio settings as map
  Map<String, dynamic> getSettings() {
    return {
      'soundEnabled': _soundEnabled,
      'musicEnabled': _musicEnabled,
      'soundVolume': _soundVolume,
      'musicVolume': _musicVolume,
    };
  }
  
  /// Load audio settings from map
  void loadSettings(Map<String, dynamic> settings) {
    _soundEnabled = settings['soundEnabled'] ?? true;
    _musicEnabled = settings['musicEnabled'] ?? true;
    _soundVolume = (settings['soundVolume'] ?? 1.0).clamp(0.0, 1.0);
    _musicVolume = (settings['musicVolume'] ?? 0.7).clamp(0.0, 1.0);
  }
}
