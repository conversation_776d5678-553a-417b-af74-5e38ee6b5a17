import 'package:flutter/material.dart';
import 'package:flame/game.dart';

import 'game/angry_birds_game.dart';

void main() {
  runApp(const AngryBirdsApp());
}

class AngryBirdsApp extends StatelessWidget {
  const AngryBirdsApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Angry Birds Game',
      theme: ThemeData(
        colorScheme: ColorScheme.fromSeed(seedColor: Colors.red),
        useMaterial3: true,
      ),
      home: const GameScreen(),
      debugShowCheckedModeBanner: false,
    );
  }
}

class GameScreen extends StatefulWidget {
  const GameScreen({super.key});

  @override
  State<GameScreen> createState() => _GameScreenState();
}

class _GameScreenState extends State<GameScreen> {
  late AngryBirdsGame game;

  @override
  void initState() {
    super.initState();
    game = AngryBirdsGame();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: GameWidget<AngryBirdsGame>.controlled(
        gameFactory: () => game,
      ),
    );
  }
}
