import 'package:flame/components.dart';
import 'package:flame_forge2d/flame_forge2d.dart';
import 'package:flutter/material.dart';

/// Ground component that provides the base surface for the game
class GroundComponent extends BodyComponent {
  static const double groundHeight = 1.0;
  static const double worldWidth = 20.0;
  
  @override
  Body createBody() {
    final bodyDef = BodyDef(
      type: BodyType.static,
      position: Vector2(0, 3.0), // Position ground below world center
    );
    
    final body = world.createBody(bodyDef);
    
    // Create ground shape
    final shape = PolygonShape()
      ..setAsBox(worldWidth / 2, groundHeight / 2, Vector2.zero(), 0);
    
    final fixtureDef = FixtureDef(shape)
      ..friction = 0.7
      ..restitution = 0.1;
    
    body.createFixture(fixtureDef);
    
    return body;
  }
  
  @override
  void render(Canvas canvas) {
    super.render(canvas);
    
    // Draw ground
    final paint = Paint()
      ..color = Colors.green[700]!
      ..style = PaintingStyle.fill;
    
    canvas.drawRect(
      Rect.fromCenter(
        center: Offset.zero,
        width: worldWidth,
        height: groundHeight,
      ),
      paint,
    );
    
    // Draw grass texture
    final grassPaint = Paint()
      ..color = Colors.green[800]!
      ..strokeWidth = 0.05
      ..style = PaintingStyle.stroke;
    
    // Simple grass lines
    for (double x = -worldWidth / 2; x < worldWidth / 2; x += 0.2) {
      canvas.drawLine(
        Offset(x, -groundHeight / 2),
        Offset(x, -groundHeight / 2 - 0.1),
        grassPaint,
      );
    }
  }
}
