import 'package:flame/components.dart';
import 'package:flame_forge2d/flame_forge2d.dart';
import 'package:flutter/material.dart';

/// Base class for all block types
abstract class BlockComponent extends BodyComponent with ContactCallbacks {
  final String blockType;
  final Vector2 blockSize;
  final double maxHealth;
  final Color color;
  final double density;
  final Vector2 initialPosition;
  final double initialRotation;

  // Block state
  double health;
  bool isDestroyed = false;

  BlockComponent({
    required this.blockType,
    required this.blockSize,
    required this.maxHealth,
    required this.color,
    required this.density,
    Vector2? position,
    double rotation = 0.0,
  }) : health = maxHealth,
       initialPosition = position ?? Vector2.zero(),
       initialRotation = rotation;
  
  @override
  Body createBody() {
    final bodyDef = BodyDef(
      type: BodyType.dynamic,
      position: initialPosition,
      angle: initialRotation,
    );
    
    final body = world.createBody(bodyDef);
    
    // Create block shape
    final shape = PolygonShape()
      ..setAsBox(blockSize.x / 2, blockSize.y / 2, Vector2.zero(), 0);
    
    final fixtureDef = FixtureDef(shape)
      ..density = density
      ..friction = 0.6
      ..restitution = 0.1;
    
    body.createFixture(fixtureDef);
    
    return body;
  }
  
  /// Take damage
  void takeDamage(double damage) {
    if (isDestroyed) return;
    
    health -= damage;
    
    // Visual feedback for damage
    onDamaged(damage);
    
    if (health <= 0) {
      destroy();
    }
  }
  
  /// Called when block takes damage (for visual effects)
  void onDamaged(double damage) {
    // Could add crack effects, particles, etc.
  }
  
  /// Destroy the block
  void destroy() {
    if (isDestroyed) return;
    
    isDestroyed = true;
    
    // Add destruction effects
    onDestroyed();
    
    // Award points
    awardPoints();
    
    // Remove from game
    removeFromParent();
  }
  
  /// Called when block is destroyed (for effects)
  void onDestroyed() {
    // Could add break effects, sound, debris, etc.
  }
  
  /// Award points for destroying this block
  void awardPoints() {
    int points = getPointValue();
    // TODO: Add points to game score
  }
  
  /// Get point value for this block type
  int getPointValue() {
    switch (blockType) {
      case 'wood':
        return 500;
      case 'stone':
        return 1000;
      case 'ice':
        return 750;
      case 'glass':
        return 250;
      default:
        return 500;
    }
  }
  
  /// Get health percentage (0.0 to 1.0)
  double getHealthPercentage() {
    return (health / maxHealth).clamp(0.0, 1.0);
  }
  
  /// Get damage resistance multiplier
  double getDamageResistance() {
    switch (blockType) {
      case 'wood':
        return 1.0;
      case 'stone':
        return 0.5; // Stone takes half damage
      case 'ice':
        return 1.5; // Ice takes more damage
      case 'glass':
        return 2.0; // Glass is very fragile
      default:
        return 1.0;
    }
  }
  
  @override
  void beginContact(Object other, Contact contact) {
    super.beginContact(other, contact);
    
    // Handle collision damage
    if (other is BodyComponent) {
      double impactForce = body.linearVelocity.length;
      if (impactForce > 1.0) {
        double damage = impactForce * 10 * getDamageResistance();
        takeDamage(damage);
      }
    }
  }
  
  @override
  void render(Canvas canvas) {
    super.render(canvas);
    
    if (isDestroyed) return;
    
    // Get current health percentage for visual damage
    double healthPercent = getHealthPercentage();
    
    // Draw block with damage effects
    drawBlock(canvas, healthPercent);
    
    // Draw cracks based on damage
    if (healthPercent < 0.7) {
      drawCracks(canvas, 1.0 - healthPercent);
    }
  }
  
  /// Draw the block (override in subclasses for different materials)
  void drawBlock(Canvas canvas, double healthPercent) {
    // Base color gets darker as health decreases
    Color currentColor = Color.lerp(color.withOpacity(0.7), color, healthPercent) ?? color;
    
    final blockPaint = Paint()
      ..color = currentColor
      ..style = PaintingStyle.fill;
    
    // Draw main block
    canvas.drawRect(
      Rect.fromCenter(
        center: Offset.zero,
        width: blockSize.x,
        height: blockSize.y,
      ),
      blockPaint,
    );
    
    // Draw outline
    final outlinePaint = Paint()
      ..color = Colors.black
      ..style = PaintingStyle.stroke
      ..strokeWidth = 0.02;
    
    canvas.drawRect(
      Rect.fromCenter(
        center: Offset.zero,
        width: blockSize.x,
        height: blockSize.y,
      ),
      outlinePaint,
    );
    
    // Draw material-specific details
    drawMaterialDetails(canvas);
  }
  
  /// Draw material-specific details (override in subclasses)
  void drawMaterialDetails(Canvas canvas) {
    // Default: no special details
  }
  
  /// Draw cracks based on damage level
  void drawCracks(Canvas canvas, double damageLevel) {
    final crackPaint = Paint()
      ..color = Colors.black.withOpacity(0.6)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 0.01;
    
    // Draw random-looking cracks
    if (damageLevel > 0.3) {
      // First crack
      canvas.drawLine(
        Offset(-blockSize.x * 0.3, -blockSize.y * 0.4),
        Offset(blockSize.x * 0.2, blockSize.y * 0.3),
        crackPaint,
      );
    }
    
    if (damageLevel > 0.6) {
      // Second crack
      canvas.drawLine(
        Offset(blockSize.x * 0.4, -blockSize.y * 0.2),
        Offset(-blockSize.x * 0.1, blockSize.y * 0.4),
        crackPaint,
      );
    }
    
    if (damageLevel > 0.8) {
      // Third crack
      canvas.drawLine(
        Offset(-blockSize.x * 0.4, blockSize.y * 0.1),
        Offset(blockSize.x * 0.3, -blockSize.y * 0.3),
        crackPaint,
      );
    }
  }
}

/// Wood block - medium strength, brown color
class WoodBlock extends BlockComponent {
  WoodBlock({Vector2? position, double rotation = 0.0})
      : super(
          blockType: 'wood',
          blockSize: Vector2(1.0, 0.5),
          maxHealth: 75.0,
          color: Colors.brown[600]!,
          density: 0.8,
          position: position,
          rotation: rotation,
        );
  
  @override
  void drawMaterialDetails(Canvas canvas) {
    // Draw wood grain
    final grainPaint = Paint()
      ..color = Colors.brown[800]!.withOpacity(0.3)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 0.01;
    
    // Horizontal grain lines
    for (double y = -blockSize.y * 0.3; y <= blockSize.y * 0.3; y += 0.1) {
      canvas.drawLine(
        Offset(-blockSize.x * 0.4, y),
        Offset(blockSize.x * 0.4, y),
        grainPaint,
      );
    }
  }
}

/// Stone block - high strength, gray color
class StoneBlock extends BlockComponent {
  StoneBlock({Vector2? position, double rotation = 0.0})
      : super(
          blockType: 'stone',
          blockSize: Vector2(1.0, 0.5),
          maxHealth: 150.0,
          color: Colors.grey[600]!,
          density: 1.5,
          position: position,
          rotation: rotation,
        );
  
  @override
  void drawMaterialDetails(Canvas canvas) {
    // Draw stone texture with small dots
    final texturePaint = Paint()
      ..color = Colors.grey[800]!.withOpacity(0.4)
      ..style = PaintingStyle.fill;
    
    // Random-looking dots for stone texture
    final dots = [
      Offset(-blockSize.x * 0.2, -blockSize.y * 0.1),
      Offset(blockSize.x * 0.3, blockSize.y * 0.2),
      Offset(-blockSize.x * 0.1, blockSize.y * 0.3),
      Offset(blockSize.x * 0.1, -blockSize.y * 0.3),
    ];
    
    for (Offset dot in dots) {
      canvas.drawCircle(dot, 0.02, texturePaint);
    }
  }
}

/// Ice block - low strength, light blue color
class IceBlock extends BlockComponent {
  IceBlock({Vector2? position, double rotation = 0.0})
      : super(
          blockType: 'ice',
          blockSize: Vector2(1.0, 0.5),
          maxHealth: 50.0,
          color: Colors.lightBlue[200]!,
          density: 0.6,
          position: position,
          rotation: rotation,
        );
  
  @override
  void drawMaterialDetails(Canvas canvas) {
    // Draw ice shine effect
    final shinePaint = Paint()
      ..color = Colors.white.withOpacity(0.6)
      ..style = PaintingStyle.fill;
    
    // Diagonal shine
    final shinePath = Path()
      ..moveTo(-blockSize.x * 0.3, -blockSize.y * 0.4)
      ..lineTo(-blockSize.x * 0.1, -blockSize.y * 0.4)
      ..lineTo(blockSize.x * 0.2, blockSize.y * 0.1)
      ..lineTo(blockSize.x * 0.0, blockSize.y * 0.1)
      ..close();
    
    canvas.drawPath(shinePath, shinePaint);
  }
}

/// Glass block - very low strength, transparent
class GlassBlock extends BlockComponent {
  GlassBlock({Vector2? position, double rotation = 0.0})
      : super(
          blockType: 'glass',
          blockSize: Vector2(1.0, 0.5),
          maxHealth: 25.0,
          color: Colors.cyan[100]!.withOpacity(0.7),
          density: 0.4,
          position: position,
          rotation: rotation,
        );
  
  @override
  void drawMaterialDetails(Canvas canvas) {
    // Draw glass reflection
    final reflectionPaint = Paint()
      ..color = Colors.white.withOpacity(0.8)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 0.02;
    
    // Vertical reflection line
    canvas.drawLine(
      Offset(-blockSize.x * 0.3, -blockSize.y * 0.4),
      Offset(-blockSize.x * 0.3, blockSize.y * 0.4),
      reflectionPaint,
    );
  }
}
