import 'package:flutter_test/flutter_test.dart';
import 'package:angry_birds_game/levels/level_data.dart';

void main() {
  group('LevelData Tests', () {
    test('from<PERSON>son creates correct LevelData', () {
      Map<String, dynamic> json = {
        'levelNumber': 1,
        'name': 'Test Level',
        'description': 'A test level',
        'birds': [
          {'type': 'red', 'position': {'x': -8.0, 'y': -2.0}}
        ],
        'pigs': [
          {'type': 'basic', 'position': {'x': 5.0, 'y': -2.0}, 'health': 50}
        ],
        'blocks': [
          {'type': 'wood', 'position': {'x': 4.0, 'y': -2.5}, 'rotation': 0.0}
        ],
        'oneStarScore': 10000,
        'twoStarScore': 20000,
        'threeStarScore': 30000,
      };

      LevelData level = LevelData.fromJson(json);

      expect(level.levelNumber, equals(1));
      expect(level.name, equals('Test Level'));
      expect(level.description, equals('A test level'));
      expect(level.birds.length, equals(1));
      expect(level.pigs.length, equals(1));
      expect(level.blocks.length, equals(1));
      expect(level.oneStarScore, equals(10000));
      expect(level.twoStarScore, equals(20000));
      expect(level.threeStarScore, equals(30000));
    });

    test('toJson creates correct JSON', () {
      LevelData level = LevelData(
        levelNumber: 1,
        name: 'Test Level',
        description: 'A test level',
        birds: [
          BirdData(type: 'red', position: {'x': -8.0, 'y': -2.0})
        ],
        pigs: [
          PigData(type: 'basic', position: {'x': 5.0, 'y': -2.0}, health: 50)
        ],
        blocks: [
          BlockData(type: 'wood', position: {'x': 4.0, 'y': -2.5}, rotation: 0.0)
        ],
        oneStarScore: 10000,
        twoStarScore: 20000,
        threeStarScore: 30000,
      );

      Map<String, dynamic> json = level.toJson();

      expect(json['levelNumber'], equals(1));
      expect(json['name'], equals('Test Level'));
      expect(json['description'], equals('A test level'));
      expect(json['birds'], isA<List>());
      expect(json['pigs'], isA<List>());
      expect(json['blocks'], isA<List>());
    });
  });

  group('BirdData Tests', () {
    test('fromJson creates correct BirdData', () {
      Map<String, dynamic> json = {
        'type': 'red',
        'position': {'x': -8.0, 'y': -2.0},
        'properties': {'special': true}
      };

      BirdData bird = BirdData.fromJson(json);

      expect(bird.type, equals('red'));
      expect(bird.x, equals(-8.0));
      expect(bird.y, equals(-2.0));
      expect(bird.properties, isNotNull);
    });

    test('toJson creates correct JSON', () {
      BirdData bird = BirdData(
        type: 'red',
        position: {'x': -8.0, 'y': -2.0},
        properties: {'special': true},
      );

      Map<String, dynamic> json = bird.toJson();

      expect(json['type'], equals('red'));
      expect(json['position']['x'], equals(-8.0));
      expect(json['position']['y'], equals(-2.0));
      expect(json['properties']['special'], equals(true));
    });

    test('position getters work correctly', () {
      BirdData bird = BirdData(
        type: 'red',
        position: {'x': -8.0, 'y': -2.0},
      );

      expect(bird.x, equals(-8.0));
      expect(bird.y, equals(-2.0));
    });
  });

  group('PigData Tests', () {
    test('fromJson creates correct PigData', () {
      Map<String, dynamic> json = {
        'type': 'basic',
        'position': {'x': 5.0, 'y': -2.0},
        'health': 100,
        'rotation': 1.57,
      };

      PigData pig = PigData.fromJson(json);

      expect(pig.type, equals('basic'));
      expect(pig.x, equals(5.0));
      expect(pig.y, equals(-2.0));
      expect(pig.health, equals(100));
      expect(pig.rotation, equals(1.57));
    });

    test('toJson creates correct JSON', () {
      PigData pig = PigData(
        type: 'basic',
        position: {'x': 5.0, 'y': -2.0},
        health: 100,
        rotation: 1.57,
      );

      Map<String, dynamic> json = pig.toJson();

      expect(json['type'], equals('basic'));
      expect(json['position']['x'], equals(5.0));
      expect(json['position']['y'], equals(-2.0));
      expect(json['health'], equals(100));
      expect(json['rotation'], equals(1.57));
    });
  });

  group('BlockData Tests', () {
    test('fromJson creates correct BlockData', () {
      Map<String, dynamic> json = {
        'type': 'wood',
        'position': {'x': 4.0, 'y': -2.5},
        'rotation': 0.0,
        'size': {'width': 1.0, 'height': 0.5},
      };

      BlockData block = BlockData.fromJson(json);

      expect(block.type, equals('wood'));
      expect(block.x, equals(4.0));
      expect(block.y, equals(-2.5));
      expect(block.rotation, equals(0.0));
      expect(block.width, equals(1.0));
      expect(block.height, equals(0.5));
    });

    test('toJson creates correct JSON', () {
      BlockData block = BlockData(
        type: 'wood',
        position: {'x': 4.0, 'y': -2.5},
        rotation: 0.0,
        size: {'width': 1.0, 'height': 0.5},
      );

      Map<String, dynamic> json = block.toJson();

      expect(json['type'], equals('wood'));
      expect(json['position']['x'], equals(4.0));
      expect(json['position']['y'], equals(-2.5));
      expect(json['rotation'], equals(0.0));
      expect(json['size']['width'], equals(1.0));
      expect(json['size']['height'], equals(0.5));
    });

    test('size getters work correctly with defaults', () {
      BlockData block = BlockData(
        type: 'wood',
        position: {'x': 4.0, 'y': -2.5},
      );

      expect(block.width, equals(1.0));
      expect(block.height, equals(1.0));
    });
  });

  group('LevelValidator Tests', () {
    test('validateLevel returns no errors for valid level', () {
      LevelData level = LevelData(
        levelNumber: 1,
        name: 'Valid Level',
        description: 'A valid test level',
        birds: [
          BirdData(type: 'red', position: {'x': -8.0, 'y': -2.0})
        ],
        pigs: [
          PigData(type: 'basic', position: {'x': 5.0, 'y': -2.0}, health: 50)
        ],
        blocks: [
          BlockData(type: 'wood', position: {'x': 4.0, 'y': -2.5})
        ],
        oneStarScore: 10000,
        twoStarScore: 20000,
        threeStarScore: 30000,
      );

      List<String> errors = LevelValidator.validateLevel(level);
      expect(errors, isEmpty);
    });

    test('validateLevel returns errors for invalid level', () {
      LevelData level = LevelData(
        levelNumber: 1,
        name: 'Invalid Level',
        description: 'An invalid test level',
        birds: [], // No birds - should cause error
        pigs: [], // No pigs - should cause error
        blocks: [],
        oneStarScore: 30000, // Higher than two star - should cause error
        twoStarScore: 20000,
        threeStarScore: 10000, // Lower than two star - should cause error
      );

      List<String> errors = LevelValidator.validateLevel(level);
      expect(errors.length, greaterThan(0));
      expect(errors.any((e) => e.contains('bird')), isTrue);
      expect(errors.any((e) => e.contains('pig')), isTrue);
    });

    test('validateLevel catches invalid bird types', () {
      LevelData level = LevelData(
        levelNumber: 1,
        name: 'Invalid Bird Level',
        description: 'Level with invalid bird',
        birds: [
          BirdData(type: 'invalid_bird', position: {'x': -8.0, 'y': -2.0})
        ],
        pigs: [
          PigData(type: 'basic', position: {'x': 5.0, 'y': -2.0}, health: 50)
        ],
        blocks: [],
        oneStarScore: 10000,
        twoStarScore: 20000,
        threeStarScore: 30000,
      );

      List<String> errors = LevelValidator.validateLevel(level);
      expect(errors.any((e) => e.contains('Invalid bird type')), isTrue);
    });
  });
}
