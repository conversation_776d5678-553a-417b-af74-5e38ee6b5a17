import 'package:flame/components.dart';
import 'package:flame_forge2d/flame_forge2d.dart';
import 'package:flutter/material.dart';
import 'dart:ui';

import 'slingshot_component.dart';
import 'ground_component.dart';
import 'background_component.dart';

/// World component that manages the game world and its components
class WorldComponent extends Component with HasGameRef<Forge2DGame> {
  late SlingshotComponent slingshot;
  late GroundComponent ground;
  late BackgroundComponent background;
  
  // World boundaries
  static const double worldWidth = 20.0; // meters
  static const double worldHeight = 15.0; // meters
  
  // Camera settings
  late CameraComponent camera;
  
  @override
  Future<void> onLoad() async {
    super.onLoad();
    
    // Set up camera
    _setupCamera();
    
    // Create world components
    await _createWorldComponents();
  }
  
  void _setupCamera() {
    // Camera setup will be handled by the game itself
    // For now, we'll use the default camera
  }
  
  Future<void> _createWorldComponents() async {
    // Create background
    background = BackgroundComponent();
    add(background);
    
    // Create ground
    ground = GroundComponent();
    add(ground);
    
    // Create slingshot
    slingshot = SlingshotComponent(
      position: Vector2(-worldWidth / 3, 0),
    );
    add(slingshot);
  }
  
  /// Handle tap events
  void handleTap(Vector2 position) {
    // Convert screen position to world position
    Vector2 worldPosition = screenToWorld(position);
    
    // Check if tap is on slingshot
    if (slingshot.containsPoint(worldPosition)) {
      slingshot.handleTap(worldPosition);
    }
  }
  
  /// Handle pan events
  void handlePan(Vector2 position) {
    Vector2 worldPosition = screenToWorld(position);
    slingshot.handlePan(worldPosition);
  }
  
  /// Handle pan end events
  void handlePanEnd() {
    slingshot.handlePanEnd();
  }
  
  /// Convert screen coordinates to world coordinates
  Vector2 screenToWorld(Vector2 screenPosition) {
    // This is a simplified conversion
    // In a real implementation, you'd use the camera's transform
    double scaleX = worldWidth / gameRef.size.x;
    double scaleY = worldHeight / gameRef.size.y;
    
    return Vector2(
      (screenPosition.x - gameRef.size.x / 2) * scaleX,
      (screenPosition.y - gameRef.size.y / 2) * scaleY,
    );
  }
  
  /// Convert world coordinates to screen coordinates
  Vector2 worldToScreen(Vector2 worldPosition) {
    double scaleX = gameRef.size.x / worldWidth;
    double scaleY = gameRef.size.y / worldHeight;
    
    return Vector2(
      worldPosition.x * scaleX + gameRef.size.x / 2,
      worldPosition.y * scaleY + gameRef.size.y / 2,
    );
  }
  
  /// Add a bird to the world
  void addBird(Component bird) {
    add(bird);
  }
  
  /// Add a pig to the world
  void addPig(Component pig) {
    add(pig);
  }
  
  /// Add a block to the world
  void addBlock(Component block) {
    add(block);
  }
  
  /// Remove all dynamic objects (birds, pigs, blocks)
  void clearDynamicObjects() {
    removeWhere((component) => 
        component != background &&
        component != ground &&
        component != slingshot);
  }
  
  /// Get all pigs in the world
  List<Component> getPigs() {
    return children.where((component) => 
        component.runtimeType.toString().contains('Pig')).toList();
  }
  
  /// Get all birds in the world
  List<Component> getBirds() {
    return children.where((component) => 
        component.runtimeType.toString().contains('Bird')).toList();
  }
  
  /// Get all blocks in the world
  List<Component> getBlocks() {
    return children.where((component) => 
        component.runtimeType.toString().contains('Block')).toList();
  }
  
  /// Check if all pigs are destroyed
  bool areAllPigsDestroyed() {
    return getPigs().isEmpty;
  }
  
  /// Get slingshot position
  Vector2 getSlingshotPosition() {
    return slingshot.position;
  }
  
  /// Focus camera on a specific position
  void focusCamera(Vector2 position) {
    camera.moveTo(position);
  }
  
  /// Reset camera to default position
  void resetCamera() {
    camera.moveTo(Vector2.zero());
  }
  
  @override
  void update(double dt) {
    super.update(dt);
    
    // Update camera to follow action if needed
    _updateCamera(dt);
  }
  
  void _updateCamera(double dt) {
    // Camera update logic will be implemented later
    // For now, keep camera static
  }
}
