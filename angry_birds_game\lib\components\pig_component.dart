import 'package:flame/components.dart';
import 'package:flame_forge2d/flame_forge2d.dart';
import 'package:flutter/material.dart';

/// Base class for all pig types
abstract class PigComponent extends BodyComponent with ContactCallbacks {
  final String pigType;
  final double radius;
  final double maxHealth;
  final Color color;
  final Vector2 initialPosition;

  // Pig state
  double health;
  bool isDestroyed = false;

  PigComponent({
    required this.pigType,
    required this.radius,
    required this.maxHealth,
    required this.color,
    Vector2? position,
  }) : health = maxHealth,
       initialPosition = position ?? Vector2.zero();
  
  @override
  Body createBody() {
    final bodyDef = BodyDef(
      type: BodyType.dynamic,
      position: initialPosition,
    );
    
    final body = world.createBody(bodyDef);
    
    // Create pig shape
    final shape = CircleShape()..radius = radius;
    
    final fixtureDef = FixtureDef(shape)
      ..density = 0.8
      ..friction = 0.5
      ..restitution = 0.2;
    
    body.createFixture(fixtureDef);
    
    return body;
  }
  
  /// Take damage
  void takeDamage(double damage) {
    if (isDestroyed) return;
    
    health -= damage;
    
    // Visual feedback for damage
    onDamaged(damage);
    
    if (health <= 0) {
      destroy();
    }
  }
  
  /// Called when pig takes damage (for visual effects)
  void onDamaged(double damage) {
    // Could add particle effects, screen shake, etc.
  }
  
  /// Destroy the pig
  void destroy() {
    if (isDestroyed) return;
    
    isDestroyed = true;
    
    // Add destruction effects
    onDestroyed();
    
    // Award points
    awardPoints();
    
    // Remove from game
    removeFromParent();
  }
  
  /// Called when pig is destroyed (for effects)
  void onDestroyed() {
    // Could add explosion effects, sound, etc.
  }
  
  /// Award points for destroying this pig
  void awardPoints() {
    int points = getPointValue();
    // TODO: Add points to game score
  }
  
  /// Get point value for this pig type
  int getPointValue() {
    switch (pigType) {
      case 'basic':
        return 5000;
      case 'helmet':
        return 10000;
      case 'king':
        return 15000;
      default:
        return 5000;
    }
  }
  
  /// Get health percentage (0.0 to 1.0)
  double getHealthPercentage() {
    return (health / maxHealth).clamp(0.0, 1.0);
  }
  
  @override
  void beginContact(Object other, Contact contact) {
    super.beginContact(other, contact);
    
    // Handle collision damage
    if (other is BodyComponent) {
      double impactForce = body.linearVelocity.length;
      if (impactForce > 2.0) {
        takeDamage(impactForce * 5);
      }
    }
  }
  
  @override
  void render(Canvas canvas) {
    super.render(canvas);
    
    if (isDestroyed) return;
    
    // Get current health percentage for color intensity
    double healthPercent = getHealthPercentage();
    Color currentColor = Color.lerp(Colors.red, color, healthPercent) ?? color;
    
    // Draw pig body
    final bodyPaint = Paint()
      ..color = currentColor
      ..style = PaintingStyle.fill;
    
    canvas.drawCircle(Offset.zero, radius, bodyPaint);
    
    // Draw pig outline
    final outlinePaint = Paint()
      ..color = Colors.black
      ..style = PaintingStyle.stroke
      ..strokeWidth = 0.05;
    
    canvas.drawCircle(Offset.zero, radius, outlinePaint);
    
    // Draw eyes
    final eyePaint = Paint()
      ..color = Colors.white
      ..style = PaintingStyle.fill;
    
    canvas.drawCircle(Offset(-radius * 0.3, -radius * 0.2), radius * 0.15, eyePaint);
    canvas.drawCircle(Offset(radius * 0.3, -radius * 0.2), radius * 0.15, eyePaint);
    
    // Draw pupils
    final pupilPaint = Paint()
      ..color = Colors.black
      ..style = PaintingStyle.fill;
    
    canvas.drawCircle(Offset(-radius * 0.3, -radius * 0.2), radius * 0.08, pupilPaint);
    canvas.drawCircle(Offset(radius * 0.3, -radius * 0.2), radius * 0.08, pupilPaint);
    
    // Draw snout
    final snoutPaint = Paint()
      ..color = currentColor.withOpacity(0.8)
      ..style = PaintingStyle.fill;
    
    canvas.drawOval(
      Rect.fromCenter(
        center: Offset(0, radius * 0.2),
        width: radius * 0.4,
        height: radius * 0.3,
      ),
      snoutPaint,
    );
    
    // Draw nostrils
    final nostrilPaint = Paint()
      ..color = Colors.black
      ..style = PaintingStyle.fill;
    
    canvas.drawCircle(Offset(-radius * 0.1, radius * 0.2), radius * 0.03, nostrilPaint);
    canvas.drawCircle(Offset(radius * 0.1, radius * 0.2), radius * 0.03, nostrilPaint);
    
    // Draw additional features based on pig type
    drawSpecialFeatures(canvas);
  }
  
  /// Draw special features for different pig types (override in subclasses)
  void drawSpecialFeatures(Canvas canvas) {
    // Default: no special features
  }
}

/// Basic pig - standard green pig
class BasicPig extends PigComponent {
  BasicPig({Vector2? position})
      : super(
          pigType: 'basic',
          radius: 0.3,
          maxHealth: 50.0,
          color: Colors.green,
          position: position,
        );
}

/// Helmet pig - pig with protective helmet
class HelmetPig extends PigComponent {
  HelmetPig({Vector2? position})
      : super(
          pigType: 'helmet',
          radius: 0.32,
          maxHealth: 100.0,
          color: Colors.green,
          position: position,
        );
  
  @override
  void takeDamage(double damage) {
    // Helmet reduces damage by 30%
    super.takeDamage(damage * 0.7);
  }
  
  @override
  void drawSpecialFeatures(Canvas canvas) {
    // Draw helmet
    final helmetPaint = Paint()
      ..color = Colors.grey[700]!
      ..style = PaintingStyle.fill;
    
    // Helmet main part
    canvas.drawArc(
      Rect.fromCenter(
        center: Offset(0, -radius * 0.3),
        width: radius * 1.4,
        height: radius * 0.8,
      ),
      3.14, // Start from bottom
      3.14, // Half circle
      false,
      helmetPaint,
    );
    
    // Helmet outline
    final helmetOutlinePaint = Paint()
      ..color = Colors.black
      ..style = PaintingStyle.stroke
      ..strokeWidth = 0.03;
    
    canvas.drawArc(
      Rect.fromCenter(
        center: Offset(0, -radius * 0.3),
        width: radius * 1.4,
        height: radius * 0.8,
      ),
      3.14,
      3.14,
      false,
      helmetOutlinePaint,
    );
  }
}

/// King pig - large pig with crown
class KingPig extends PigComponent {
  KingPig({Vector2? position})
      : super(
          pigType: 'king',
          radius: 0.4,
          maxHealth: 150.0,
          color: Colors.green[600]!,
          position: position,
        );
  
  @override
  int getPointValue() {
    return 20000; // King pig is worth more points
  }
  
  @override
  void drawSpecialFeatures(Canvas canvas) {
    // Draw crown
    final crownPaint = Paint()
      ..color = Colors.yellow[700]!
      ..style = PaintingStyle.fill;
    
    // Crown base
    canvas.drawRect(
      Rect.fromLTWH(-radius * 0.6, -radius * 0.8, radius * 1.2, radius * 0.3),
      crownPaint,
    );
    
    // Crown spikes
    final crownPath = Path();
    for (int i = 0; i < 5; i++) {
      double x = -radius * 0.6 + (radius * 1.2 / 4) * i;
      crownPath.moveTo(x, -radius * 0.5);
      crownPath.lineTo(x + radius * 0.15, -radius * 0.9);
      crownPath.lineTo(x + radius * 0.3, -radius * 0.5);
    }
    
    canvas.drawPath(crownPath, crownPaint);
    
    // Crown outline
    final crownOutlinePaint = Paint()
      ..color = Colors.black
      ..style = PaintingStyle.stroke
      ..strokeWidth = 0.03;
    
    canvas.drawRect(
      Rect.fromLTWH(-radius * 0.6, -radius * 0.8, radius * 1.2, radius * 0.3),
      crownOutlinePaint,
    );
    
    canvas.drawPath(crownPath, crownOutlinePaint);
    
    // Crown jewels
    final jewelPaint = Paint()
      ..color = Colors.red
      ..style = PaintingStyle.fill;
    
    canvas.drawCircle(Offset(0, -radius * 0.65), radius * 0.08, jewelPaint);
  }
}
