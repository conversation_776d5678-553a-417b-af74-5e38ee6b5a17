import 'package:flutter/material.dart';
import 'package:flame/game.dart';

import '../game/angry_birds_game.dart';
import 'level_selection_screen.dart';

/// Main menu screen
class MainMenuScreen extends StatelessWidget {
  const MainMenuScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Color(0xFF87CEEB), // Sky blue
              Color(0xFF98FB98), // Pale green
            ],
          ),
        ),
        child: Safe<PERSON><PERSON>(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // Game Title
              const Text(
                'ANGRY BIRDS',
                style: TextStyle(
                  fontSize: 48,
                  fontWeight: FontWeight.bold,
                  color: Colors.red,
                  shadows: [
                    Shadow(
                      offset: Offset(3, 3),
                      blurRadius: 5,
                      color: Colors.black54,
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 20),
              
              // Subtitle
              const Text(
                'Flutter Edition',
                style: TextStyle(
                  fontSize: 18,
                  color: Colors.black87,
                  fontStyle: FontStyle.italic,
                ),
              ),
              const SizedBox(height: 60),
              
              // Menu Buttons
              Column(
                children: [
                  _buildMenuButton(
                    context,
                    'PLAY',
                    Colors.green,
                    () => _navigateToLevelSelection(context),
                  ),
                  const SizedBox(height: 20),
                  _buildMenuButton(
                    context,
                    'QUICK PLAY',
                    Colors.orange,
                    () => _startQuickPlay(context),
                  ),
                  const SizedBox(height: 20),
                  _buildMenuButton(
                    context,
                    'SETTINGS',
                    Colors.blue,
                    () => _showSettings(context),
                  ),
                  const SizedBox(height: 20),
                  _buildMenuButton(
                    context,
                    'ABOUT',
                    Colors.purple,
                    () => _showAbout(context),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildMenuButton(
    BuildContext context,
    String text,
    Color color,
    VoidCallback onPressed,
  ) {
    return SizedBox(
      width: 200,
      height: 50,
      child: ElevatedButton(
        onPressed: onPressed,
        style: ElevatedButton.styleFrom(
          backgroundColor: color,
          foregroundColor: Colors.white,
          elevation: 5,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(25),
          ),
        ),
        child: Text(
          text,
          style: const TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
    );
  }

  void _navigateToLevelSelection(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const LevelSelectionScreen(),
      ),
    );
  }

  void _startQuickPlay(BuildContext context) {
    // Start level 1 directly
    _startGame(context, 1);
  }

  void _startGame(BuildContext context, int levelNumber) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => GameScreen(levelNumber: levelNumber),
      ),
    );
  }

  void _showSettings(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Settings'),
        content: const Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: Icon(Icons.volume_up),
              title: Text('Sound Effects'),
              trailing: Switch(value: true, onChanged: null),
            ),
            ListTile(
              leading: Icon(Icons.music_note),
              title: Text('Background Music'),
              trailing: Switch(value: true, onChanged: null),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  void _showAbout(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('About'),
        content: const Text(
          'Angry Birds Flutter Edition\n\n'
          'A physics-based puzzle game built with Flutter and Flame.\n\n'
          'Launch birds to destroy pig fortresses and save the day!\n\n'
          'Features:\n'
          '• 10 challenging levels\n'
          '• 5 different bird types\n'
          '• Realistic physics\n'
          '• Multiple block materials\n'
          '• Star rating system',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }
}

/// Game screen wrapper
class GameScreen extends StatefulWidget {
  final int levelNumber;
  
  const GameScreen({super.key, required this.levelNumber});

  @override
  State<GameScreen> createState() => _GameScreenState();
}

class _GameScreenState extends State<GameScreen> {
  late AngryBirdsGame game;

  @override
  void initState() {
    super.initState();
    game = AngryBirdsGame();
    // Load the specified level after the game is fully initialized
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      try {
        await Future.delayed(const Duration(milliseconds: 500)); // Give time for game to initialize
        await game.loadLevel(widget.levelNumber);
      } catch (e) {
        debugPrint('Error loading level ${widget.levelNumber}: $e');
        // For now, just continue without loading a specific level
        // The game will use default components
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        children: [
          GameWidget<AngryBirdsGame>.controlled(
            gameFactory: () => game,
          ),
          // Back button
          Positioned(
            top: 40,
            left: 20,
            child: IconButton(
              onPressed: () => Navigator.pop(context),
              icon: const Icon(
                Icons.arrow_back,
                color: Colors.white,
                size: 30,
              ),
              style: IconButton.styleFrom(
                backgroundColor: Colors.black54,
                shape: const CircleBorder(),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
