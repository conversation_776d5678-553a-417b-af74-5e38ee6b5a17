/// Enum representing different game states
enum GameStateType {
  menu,
  playing,
  paused,
  levelCompleted,
  levelFailed,
  gameCompleted,
  loading,
}

/// Game state management class
class GameState {
  GameStateType _currentState = GameStateType.menu;
  int _currentLevel = 1;
  int _score = 0;
  int _totalScore = 0;
  int _lives = 3;
  List<int> _levelStars = [];
  
  // Getters
  GameStateType get currentState => _currentState;
  int get currentLevel => _currentLevel;
  int get score => _score;
  int get totalScore => _totalScore;
  int get lives => _lives;
  List<int> get levelStars => List.unmodifiable(_levelStars);
  
  // Setters
  set currentLevel(int level) {
    _currentLevel = level;
    _score = 0; // Reset level score
  }
  
  set score(int score) {
    _score = score;
    _totalScore += score;
  }
  
  /// Set the current game state
  void setState(GameStateType newState) {
    GameStateType previousState = _currentState;
    _currentState = newState;
    
    // Handle state transitions
    _handleStateTransition(previousState, newState);
  }
  
  /// Handle state transitions
  void _handleStateTransition(GameStateType from, GameStateType to) {
    switch (to) {
      case GameStateType.playing:
        // Game is now active
        break;
      case GameStateType.paused:
        // Game is paused
        break;
      case GameStateType.levelCompleted:
        // Level completed, prepare for next level
        break;
      case GameStateType.levelFailed:
        // Level failed, reduce lives
        _lives--;
        break;
      case GameStateType.gameCompleted:
        // Game completed
        break;
      default:
        break;
    }
  }
  
  /// Add stars for a completed level
  void addLevelStars(int level, int stars) {
    // Ensure the list is large enough
    while (_levelStars.length < level) {
      _levelStars.add(0);
    }
    
    // Update stars for the level (keep the highest)
    if (level > 0 && level <= _levelStars.length) {
      _levelStars[level - 1] = stars > _levelStars[level - 1] ? stars : _levelStars[level - 1];
    }
  }
  
  /// Get stars for a specific level
  int getLevelStars(int level) {
    if (level > 0 && level <= _levelStars.length) {
      return _levelStars[level - 1];
    }
    return 0;
  }
  
  /// Check if a level is unlocked
  bool isLevelUnlocked(int level) {
    if (level == 1) return true; // First level is always unlocked
    
    // Level is unlocked if previous level has at least 1 star
    return getLevelStars(level - 1) > 0;
  }
  
  /// Get total stars earned
  int getTotalStars() {
    return _levelStars.fold(0, (sum, stars) => sum + stars);
  }
  
  /// Reset game state
  void reset() {
    _currentState = GameStateType.menu;
    _currentLevel = 1;
    _score = 0;
    _totalScore = 0;
    _lives = 3;
    _levelStars.clear();
  }
  
  /// Reset current level
  void resetLevel() {
    _score = 0;
    _currentState = GameStateType.playing;
  }
  
  /// Check if game is over
  bool isGameOver() {
    return _lives <= 0;
  }
  
  /// Add extra life
  void addLife() {
    _lives++;
  }
  
  /// Get game state as string for debugging
  String getStateString() {
    return 'State: $_currentState, Level: $_currentLevel, Score: $_score, Lives: $_lives';
  }
  
  /// Save game state to persistent storage (placeholder)
  Map<String, dynamic> toJson() {
    return {
      'currentLevel': _currentLevel,
      'totalScore': _totalScore,
      'lives': _lives,
      'levelStars': _levelStars,
    };
  }
  
  /// Load game state from persistent storage (placeholder)
  void fromJson(Map<String, dynamic> json) {
    _currentLevel = json['currentLevel'] ?? 1;
    _totalScore = json['totalScore'] ?? 0;
    _lives = json['lives'] ?? 3;
    _levelStars = List<int>.from(json['levelStars'] ?? []);
  }
}
