import 'package:flame/game.dart';
import 'package:flame/events.dart';
import 'package:flame_forge2d/flame_forge2d.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import '../components/world_component.dart';
import '../levels/level_manager.dart';
import '../ui/game_hud.dart';
import '../utils/game_state.dart';
import '../audio/audio_manager.dart';

/// Main game class that extends Forge2DGame for physics support
class AngryBirdsGame extends Forge2DGame
    with HasKeyboardHandlerComponents, HasCollisionDetection, TapDetector {
  
  // Game state management
  late GameState gameState;
  late LevelManager levelManager;
  late AudioManager audioManager;
  GameHUD? gameHUD;
  WorldComponent? worldComponent;
  
  // Game configuration
  static const double gravity = 9.8;
  static const double worldScale = 10.0; // Pixels per meter
  
  // Current level
  int currentLevel = 1;
  
  @override
  Future<void> onLoad() async {
    super.onLoad();
    
    // Initialize game systems
    await _initializeGameSystems();
    
    // Set up the world
    await _setupWorld();
    
    // Load the first level
    await loadLevel(currentLevel);
  }
  
  Future<void> _initializeGameSystems() async {
    // Initialize game state
    gameState = GameState();
    
    // Initialize audio manager
    audioManager = AudioManager();
    await audioManager.initialize();
    
    // Initialize level manager
    levelManager = LevelManager(this);
    
    // Initialize HUD
    gameHUD = GameHUD(this);
    add(gameHUD!);
  }

  Future<void> _setupWorld() async {
    // Set up physics world with gravity
    world.gravity = Vector2(0, gravity);

    // Create world component
    worldComponent = WorldComponent();
    add(worldComponent!);
  }
  
  /// Load a specific level
  Future<void> loadLevel(int levelNumber) async {
    currentLevel = levelNumber;
    
    // Clear existing level
    await _clearLevel();
    
    // Load new level
    await levelManager.loadLevel(levelNumber);
    
    // Update game state
    gameState.currentLevel = levelNumber;
    gameState.setState(GameStateType.playing);
    
    // Update HUD
    gameHUD?.updateLevel(levelNumber);
  }
  
  /// Clear the current level
  Future<void> _clearLevel() async {
    // Remove all game objects except HUD and world component
    removeWhere((component) =>
        component != gameHUD &&
        component != worldComponent &&
        component.runtimeType.toString().contains('Component'));
  }
  
  /// Restart the current level
  Future<void> restartLevel() async {
    await loadLevel(currentLevel);
    audioManager.playSound('restart');
  }
  
  /// Go to next level
  Future<void> nextLevel() async {
    if (currentLevel < levelManager.totalLevels) {
      await loadLevel(currentLevel + 1);
    } else {
      // Game completed
      gameState.setState(GameStateType.gameCompleted);
    }
  }
  
  /// Pause the game
  void pauseGame() {
    gameState.setState(GameStateType.paused);
    pauseEngine();
    audioManager.pauseBackgroundMusic();
  }
  
  /// Resume the game
  void resumeGame() {
    gameState.setState(GameStateType.playing);
    resumeEngine();
    audioManager.resumeBackgroundMusic();
  }
  
  /// Handle tap events
  @override
  bool onTapDown(TapDownInfo info) {
    if (gameState.currentState == GameStateType.playing) {
      // Handle slingshot interaction
      worldComponent?.handleTap(info.eventPosition.global);
    }
    return true;
  }

  /// Handle pan events for slingshot
  @override
  bool onPanUpdate(DragUpdateInfo info) {
    if (gameState.currentState == GameStateType.playing) {
      worldComponent?.handlePan(info.eventPosition.global);
    }
    return true;
  }

  /// Handle pan end for slingshot release
  @override
  bool onPanEnd(DragEndInfo info) {
    if (gameState.currentState == GameStateType.playing) {
      worldComponent?.handlePanEnd();
    }
    return true;
  }
  
  /// Check if level is completed
  void checkLevelCompletion() {
    if (levelManager.isLevelCompleted()) {
      gameState.setState(GameStateType.levelCompleted);
      audioManager.playSound('level_complete');
      
      // Calculate score and stars
      int score = _calculateScore();
      int stars = _calculateStars(score);
      
      gameHUD?.showLevelComplete(score, stars);
    }
  }

  /// Check if level is failed
  void checkLevelFailed() {
    if (levelManager.isLevelFailed()) {
      gameState.setState(GameStateType.levelFailed);
      audioManager.playSound('level_failed');
      gameHUD?.showLevelFailed();
    }
  }
  
  /// Calculate score based on remaining birds and destruction
  int _calculateScore() {
    int baseScore = levelManager.getDestructionScore();
    int birdBonus = levelManager.getRemainingBirdsBonus();
    return baseScore + birdBonus;
  }
  
  /// Calculate stars based on score
  int _calculateStars(int score) {
    if (score >= levelManager.getThreeStarScore()) return 3;
    if (score >= levelManager.getTwoStarScore()) return 2;
    if (score >= levelManager.getOneStarScore()) return 1;
    return 0;
  }
  
  @override
  void update(double dt) {
    super.update(dt);
    
    // Update game systems
    if (gameState.currentState == GameStateType.playing) {
      levelManager.update(dt);
      
      // Check win/lose conditions
      checkLevelCompletion();
      checkLevelFailed();
    }
  }
  
  @override
  void render(Canvas canvas) {
    super.render(canvas);
    
    // Additional rendering if needed
  }
}
